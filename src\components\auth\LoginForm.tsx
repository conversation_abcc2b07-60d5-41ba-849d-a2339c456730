'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { signIn, signInWithUsername } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'

const loginSchema = z.object({
  usernameOrEmail: z.string().min(1, 'Please enter your username or email'),
  password: z.string().min(1, 'Password is required'),
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onSuccess?: (user: any) => void
  onSwitchToRegister?: () => void
}

export function LoginForm({ onSuccess, onSwitchToRegister }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    try {
      const input = data.usernameOrEmail
      const isEmail = input.includes('@')

      let authData, error

      if (isEmail) {
        // Try email/password login (Supabase Auth)
        const result = await signIn(input, data.password)
        authData = result.data
        error = result.error
      } else {
        // Try username/password login (custom auth against users table)
        const result = await signInWithUsername(input, data.password)
        authData = result.data
        error = result.error
      }

      if (error) {
        toast({
          title: 'Login failed',
          description: error.message || 'Invalid credentials',
          variant: 'destructive',
        })
        return
      }

      if (authData?.user) {
        toast({
          title: 'Welcome back!',
          description: 'You have successfully logged in.',
        })
        onSuccess?.(authData.user)
      }
    } catch (error) {
      toast({
        title: 'Login failed',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">Welcome to DasWos AI</CardTitle>
        <CardDescription>
          Sign in to start chatting with your AI shopping assistant
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              {...register('email')}
              type="email"
              placeholder="Enter your email"
              error={errors.email?.message}
              disabled={isLoading}
            />
          </div>

          <div>
            <Input
              {...register('password')}
              type="password"
              placeholder="Enter your password"
              error={errors.password?.message}
              disabled={isLoading}
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            loading={isLoading}
            disabled={isLoading}
          >
            Sign In
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            Don't have an account?{' '}
            <button
              type="button"
              onClick={onSwitchToRegister}
              className="text-primary hover:underline font-medium"
            >
              Sign up
            </button>
          </p>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-muted-foreground">
            Use your existing DasWos marketplace credentials
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
