"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 250,\n            y: 250\n        },\n        velocity: {\n            x: 1,\n            y: 0.5\n        },\n        targetPosition: {\n            x: 250,\n            y: 250\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls\n            const margin = 80 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally\n            if (Math.random() < 0.005) {\n                robotState.velocity.x = (Math.random() - 0.5) * 3;\n                robotState.velocity.y = (Math.random() - 0.5) * 3;\n                robotState.rotation += (Math.random() - 0.5) * 0.5;\n            }\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation = Math.sin(robotState.dancePhase * 1.5) * 0.4;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors\n        const headColor = \"#4A90E2\";\n        const bodyColor = \"#F5F5F5\";\n        const eyeColor = \"#2C3E50\";\n        const mouthColor = \"#E74C3C\";\n        const armColor = \"#34495E\";\n        // Draw robot body (main torso)\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#2980B9\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes\n        ctx.fillStyle = eyeColor;\n        const eyeY = -70;\n        const eyeSize = 8 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 4;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (line)\n            ctx.moveTo(-12, -45);\n            ctx.lineTo(12, -45);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 500,\n            height: 500,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});