"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 300,\n            y: 300\n        },\n        velocity: {\n            x: 0.8,\n            y: 0.6\n        },\n        targetPosition: {\n            x: 300,\n            y: 300\n        },\n        scale: scale * 1.3,\n        rotation: 0,\n        baseRotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true,\n        centerPull: 0.002 // Gentle pull toward center\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot at its current position\n            drawRobot(ctx, robotState, robotState.position.x, robotState.position.y);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Gentle pull toward center\n            const centerX = canvas.width / 2;\n            const centerY = canvas.height / 2;\n            const distanceFromCenter = Math.sqrt(Math.pow(robotState.position.x - centerX, 2) + Math.pow(robotState.position.y - centerY, 2));\n            // Apply gentle center pull when far from center\n            if (distanceFromCenter > 150) {\n                robotState.velocity.x += (centerX - robotState.position.x) * robotState.centerPull;\n                robotState.velocity.y += (centerY - robotState.position.y) * robotState.centerPull;\n            }\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls with gentler rotation\n            const margin = 100 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -0.8 // Softer bounce\n                ;\n                robotState.baseRotation += 0.05 // Gentle spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -0.8 // Softer bounce\n                ;\n                robotState.baseRotation += 0.05 // Gentle spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally (less frequent)\n            if (Math.random() < 0.003) {\n                robotState.velocity.x = (Math.random() - 0.5) * 2;\n                robotState.velocity.y = (Math.random() - 0.5) * 2;\n            }\n            // Gradually return to upright position\n            robotState.baseRotation *= 0.98 // Slowly reduce rotation\n            ;\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                robotState.isMoving = true // Allow movement when idle\n                ;\n                robotState.rotation = robotState.baseRotation + Math.sin(timeInSeconds * 1.5) * 0.05 // Gentle sway\n                ;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                robotState.isMoving = false // Stop moving when talking\n                ;\n                robotState.rotation = robotState.baseRotation // Stay upright when talking\n                ;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = robotState.baseRotation + Math.sin(timeInSeconds * 2) * 0.1 // Gentle tilt\n                ;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                robotState.isMoving = false // Stop moving when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = robotState.baseRotation + Math.sin(timeInSeconds * 0.8) * 0.15 // Gentle thinking tilt\n                ;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                robotState.isMoving = false // Stop moving when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.baseRotation += 0.03 // Continuous spinning while dancing\n                ;\n                robotState.rotation = robotState.baseRotation + Math.sin(robotState.dancePhase) * 0.2;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.05;\n                robotState.velocity.y *= 1.05;\n                robotState.isMoving = true;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = robotState.baseRotation + Math.sin(robotState.searchAngle) * 0.3;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                robotState.isMoving = true;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors - matching your robot images\n        const headColor = \"#5B9BD5\" // Blue head like in your images\n        ;\n        const bodyColor = \"#FFFFFF\" // White body\n        ;\n        const eyeColor = \"#2C3E50\" // Dark eyes\n        ;\n        const mouthColor = \"#FF6B6B\" // Red mouth\n        ;\n        const armColor = \"#4A4A4A\" // Dark gray arms\n        ;\n        // Draw robot body (main torso) - more rounded like your images\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#E0E0E0\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, 10, 55, 70, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head - more rounded and friendly\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#4A90E2\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, -50, 45, 40, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes - larger and more expressive like your robot\n        ctx.fillStyle = eyeColor;\n        const eyeY = -55;\n        const eyeSize = 6 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Add eye highlights for more life\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth - more curved and friendly\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -35, 10, 4 + robotState.mouthOpen * 8, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (curved line)\n            ctx.arc(0, -30, 8, 0.2, Math.PI - 0.2);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className, \" relative\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 600,\n            height: 600,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer w-full h-full\",\n            style: {\n                maxWidth: \"100%\",\n                maxHeight: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 376,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});