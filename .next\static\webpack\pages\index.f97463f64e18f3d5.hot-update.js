"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 250,\n            y: 250\n        },\n        velocity: {\n            x: 1,\n            y: 0.5\n        },\n        targetPosition: {\n            x: 250,\n            y: 250\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot at its current position\n            drawRobot(ctx, robotState, robotState.position.x, robotState.position.y);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls\n            const margin = 80 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally\n            if (Math.random() < 0.005) {\n                robotState.velocity.x = (Math.random() - 0.5) * 3;\n                robotState.velocity.y = (Math.random() - 0.5) * 3;\n                robotState.rotation += (Math.random() - 0.5) * 0.5;\n            }\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation += 0.05 // Continuous spinning while dancing\n                ;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.1;\n                robotState.velocity.y *= 1.1;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors\n        const headColor = \"#4A90E2\";\n        const bodyColor = \"#F5F5F5\";\n        const eyeColor = \"#2C3E50\";\n        const mouthColor = \"#E74C3C\";\n        const armColor = \"#34495E\";\n        // Draw robot body (main torso)\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#2980B9\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes\n        ctx.fillStyle = eyeColor;\n        const eyeY = -70;\n        const eyeSize = 8 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 4;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (line)\n            ctx.moveTo(-12, -45);\n            ctx.lineTo(12, -45);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 500,\n            height: 500,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 340,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});