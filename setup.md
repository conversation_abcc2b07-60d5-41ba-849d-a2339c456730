# DasWos AI Setup Guide

## Quick Start

Follow these steps to get your DasWos AI application running:

### 1. Environment Setup

Copy the environment template and fill in your credentials:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your actual values:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OpenAI Configuration  
OPENAI_API_KEY=sk-your_openai_api_key_here

# Optional: Direct database connection
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres
```

### 2. Get Your Supabase Credentials

1. Go to [supabase.com](https://supabase.com) and sign in
2. Select your existing DasWos project (or create a new one)
3. Go to Settings → API
4. Copy the Project URL and anon/public key
5. Copy the service_role key (keep this secret!)

### 3. Get Your OpenAI API Key

1. Go to [platform.openai.com](https://platform.openai.com)
2. Sign in or create an account
3. Go to API Keys section
4. Create a new secret key
5. Copy the key (starts with `sk-`)

### 4. Database Schema

Your Supabase database should have these tables:

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  trust_score INTEGER DEFAULT 50,
  verification_status TEXT DEFAULT 'unverified',
  tier TEXT DEFAULT 'guest',
  is_seller BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  price INTEGER NOT NULL, -- in cents
  image TEXT,
  trust_score INTEGER DEFAULT 50,
  sphere TEXT DEFAULT 'opensphere',
  category TEXT,
  tags TEXT[],
  seller_id UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart items table
CREATE TABLE cart_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER DEFAULT 1,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI chat tables
CREATE TABLE daswos_ai_chats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE daswos_ai_chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID REFERENCES daswos_ai_chats(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. Row Level Security (RLS)

Enable RLS on your tables for security:

```sql
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE daswos_ai_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE daswos_ai_chat_messages ENABLE ROW LEVEL SECURITY;

-- Basic policies (adjust as needed)
CREATE POLICY "Users can view their own data" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Anyone can view products" ON products
  FOR SELECT TO authenticated;

CREATE POLICY "Users can manage their cart" ON cart_items
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their chats" ON daswos_ai_chats
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their messages" ON daswos_ai_chat_messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM daswos_ai_chats 
      WHERE id = chat_id AND user_id = auth.uid()
    )
  );
```

### 6. Sample Data (Optional)

Add some sample products to test with:

```sql
INSERT INTO products (name, description, price, trust_score, sphere, category) VALUES
('Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 15000, 85, 'safesphere', 'Electronics'),
('Coffee Maker', 'Automatic drip coffee maker with programmable timer', 8000, 78, 'safesphere', 'Appliances'),
('Running Shoes', 'Comfortable running shoes for daily exercise', 12000, 82, 'opensphere', 'Sports'),
('Laptop Stand', 'Adjustable aluminum laptop stand for better ergonomics', 4500, 90, 'safesphere', 'Office'),
('Bluetooth Speaker', 'Portable Bluetooth speaker with excellent sound quality', 6000, 75, 'opensphere', 'Electronics');
```

### 7. Run the Application

```bash
# Install dependencies (if not already done)
npm install

# Start the development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

### 8. Test the Application

1. **Sign Up/Sign In**: Create a new account or use existing credentials
2. **Chat with Daswos**: Try saying "Show me some headphones" or "Add coffee maker to cart"
3. **Voice Features**: Click the microphone button to test voice input
4. **Shopping Cart**: Check that items are added to your cart
5. **Robot Animation**: Click on the robot to see animations

### Troubleshooting

**Common Issues:**

1. **"Failed to fetch" errors**: Check your Supabase URL and keys
2. **Voice not working**: Ensure HTTPS in production, check browser permissions
3. **AI responses failing**: Verify OpenAI API key and account credits
4. **Database errors**: Check RLS policies and table permissions

**Debug Steps:**

1. Check browser console for errors
2. Verify environment variables are loaded
3. Test Supabase connection in the browser network tab
4. Check OpenAI API usage in their dashboard

### Production Deployment

For production deployment:

1. Set up your domain with HTTPS
2. Update NEXTAUTH_URL in environment variables
3. Configure proper CORS settings in Supabase
4. Set up proper RLS policies for your use case
5. Monitor API usage and costs

### Support

If you encounter issues:

1. Check the README.md for detailed documentation
2. Review the troubleshooting section
3. Check GitHub issues for similar problems
4. Contact the development team

Enjoy your AI-powered shopping assistant! 🤖🛒
