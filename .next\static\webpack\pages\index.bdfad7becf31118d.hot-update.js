"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 300,\n            y: 300\n        },\n        velocity: {\n            x: 0.8,\n            y: 0.6\n        },\n        targetPosition: {\n            x: 300,\n            y: 300\n        },\n        scale: scale * 1.3,\n        rotation: 0,\n        baseRotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true,\n        centerPull: 0.002 // Gentle pull toward center\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot at its current position\n            drawRobot(ctx, robotState, robotState.position.x, robotState.position.y);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls\n            const margin = 80 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally\n            if (Math.random() < 0.005) {\n                robotState.velocity.x = (Math.random() - 0.5) * 3;\n                robotState.velocity.y = (Math.random() - 0.5) * 3;\n                robotState.rotation += (Math.random() - 0.5) * 0.5;\n            }\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                robotState.isMoving = true // Allow movement when idle\n                ;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                robotState.isMoving = false // Stop moving when talking\n                ;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                robotState.isMoving = false // Stop moving when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                robotState.isMoving = false // Stop moving when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation += 0.05 // Continuous spinning while dancing\n                ;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.1;\n                robotState.velocity.y *= 1.1;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors - matching your robot images\n        const headColor = \"#5B9BD5\" // Blue head like in your images\n        ;\n        const bodyColor = \"#FFFFFF\" // White body\n        ;\n        const eyeColor = \"#2C3E50\" // Dark eyes\n        ;\n        const mouthColor = \"#FF6B6B\" // Red mouth\n        ;\n        const armColor = \"#4A4A4A\" // Dark gray arms\n        ;\n        // Draw robot body (main torso) - more rounded like your images\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#E0E0E0\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, 10, 55, 70, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head - more rounded and friendly\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#4A90E2\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, -50, 45, 40, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes - larger and more expressive like your robot\n        ctx.fillStyle = eyeColor;\n        const eyeY = -55;\n        const eyeSize = 6 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Add eye highlights for more life\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth - more curved and friendly\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -35, 10, 4 + robotState.mouthOpen * 8, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (curved line)\n            ctx.arc(0, -30, 8, 0.2, Math.PI - 0.2);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className, \" relative\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 600,\n            height: 600,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer w-full h-full\",\n            style: {\n                maxWidth: \"100%\",\n                maxHeight: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 355,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 354,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});