'use client'

import { useEffect, useRef, useState } from 'react'
import { RobotState, AnimationState } from '@/types'

interface RobotInterfaceProps {
  state: AnimationState
  onInteraction?: (type: string, data?: any) => void
  scale?: number
  className?: string
}

export function RobotInterface({
  state = 'idle',
  onInteraction,
  scale = 0.8,
  className = ''
}: RobotInterfaceProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameRef = useRef<number>()

  // Robot animation state
  const robotStateRef = useRef({
    currentState: state,
    position: { x: 0, y: 0 },
    scale: scale,
    rotation: 0,
    headBobAmount: 0,
    bodyRotation: 0,
    armRotations: { left: 0, right: 0 },
    animationTime: 0,
    isBlinking: false,
    blinkTimer: 0,
    talkPulse: 0,
    dancePhase: 0,
    searchAngle: 0,
    eyeScale: 1,
    mouthScale: 1,
    mouthOpen: 0
  })

  // No need to load images - we'll draw the robot with canvas primitives

  // Update robot state when props change
  useEffect(() => {
    robotStateRef.current.currentState = state
    robotStateRef.current.scale = scale
  }, [state, scale])

  // Animation loop
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const animate = (timestamp: number) => {
      const robotState = robotStateRef.current
      robotState.animationTime = timestamp

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Update animation based on current state
      updateRobotAnimation(robotState, timestamp)

      // Draw robot
      drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2)

      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  const updateRobotAnimation = (robotState: any, timestamp: number) => {
    const timeInSeconds = timestamp * 0.001

    // Handle blinking
    robotState.blinkTimer += 0.016 // ~60fps
    if (robotState.blinkTimer > 3 + Math.random() * 2) { // Blink every 3-5 seconds
      robotState.isBlinking = true
      robotState.blinkTimer = 0
    }
    if (robotState.isBlinking) {
      robotState.eyeScale = Math.max(0.1, 1 - (robotState.blinkTimer * 10))
      if (robotState.blinkTimer > 0.2) {
        robotState.isBlinking = false
        robotState.eyeScale = 1
      }
    }

    switch (robotState.currentState) {
      case 'idle':
        robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8
        robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1
        robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1
        robotState.mouthOpen = 0
        break

      case 'talking':
        robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1
        robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12
        robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3
        robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3
        robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8
        robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1
        break

      case 'listening':
        robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5
        robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15
        robotState.mouthOpen = 0.2
        robotState.eyeScale = 1.2 // Wider eyes when listening
        break

      case 'thinking':
        robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6
        robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25
        robotState.mouthOpen = 0.1
        robotState.eyeScale = 0.8 // Squinted eyes when thinking
        break

      case 'dancing':
        robotState.dancePhase += 0.08
        robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15
        robotState.rotation = Math.sin(robotState.dancePhase * 1.5) * 0.4
        robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6
        robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6
        robotState.mouthOpen = 0.6 // Happy expression
        robotState.eyeScale = 1.3 // Excited eyes
        break

      case 'searching':
        robotState.searchAngle += 0.04
        robotState.rotation = Math.sin(robotState.searchAngle) * 0.4
        robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8
        robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25
        robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25
        robotState.mouthOpen = 0.3
        robotState.eyeScale = 1.1
        break
    }
  }

  const drawRobot = (ctx: CanvasRenderingContext2D, robotState: any, centerX: number, centerY: number) => {
    ctx.save()

    // Move to center and apply transformations
    ctx.translate(centerX, centerY + robotState.headBobAmount)
    ctx.scale(robotState.scale, robotState.scale)
    ctx.rotate(robotState.rotation)

    // Apply talk pulse if talking
    if (robotState.currentState === 'talking') {
      ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse)
    }

    // Robot colors
    const headColor = '#4A90E2'
    const bodyColor = '#F5F5F5'
    const eyeColor = '#2C3E50'
    const mouthColor = '#E74C3C'
    const armColor = '#34495E'

    // Draw robot body (main torso)
    ctx.fillStyle = bodyColor
    ctx.strokeStyle = '#BDC3C7'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()

    // Draw robot head
    ctx.fillStyle = headColor
    ctx.strokeStyle = '#2980B9'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()

    // Draw eyes
    ctx.fillStyle = eyeColor
    const eyeY = -70
    const eyeSize = 8 * robotState.eyeScale

    // Left eye
    ctx.beginPath()
    ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2)
    ctx.fill()

    // Right eye
    ctx.beginPath()
    ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2)
    ctx.fill()

    // Draw mouth
    ctx.strokeStyle = mouthColor
    ctx.lineWidth = 4
    ctx.beginPath()
    if (robotState.mouthOpen > 0) {
      // Open mouth (oval)
      ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2)
      ctx.stroke()
    } else {
      // Closed mouth (line)
      ctx.moveTo(-12, -45)
      ctx.lineTo(12, -45)
      ctx.stroke()
    }

    // Draw left arm
    ctx.save()
    ctx.translate(-70, 0)
    ctx.rotate(robotState.armRotations.left)
    ctx.fillStyle = armColor
    ctx.strokeStyle = '#2C3E50'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.roundRect(-8, -25, 16, 50, 8)
    ctx.fill()
    ctx.stroke()
    ctx.restore()

    // Draw right arm
    ctx.save()
    ctx.translate(70, 0)
    ctx.rotate(robotState.armRotations.right)
    ctx.fillStyle = armColor
    ctx.strokeStyle = '#2C3E50'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.roundRect(-8, -25, 16, 50, 8)
    ctx.fill()
    ctx.stroke()
    ctx.restore()

    // Draw antenna
    ctx.strokeStyle = '#7F8C8D'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.moveTo(0, -105)
    ctx.lineTo(0, -120)
    ctx.stroke()

    // Antenna tip
    ctx.fillStyle = '#E74C3C'
    ctx.beginPath()
    ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2)
    ctx.fill()

    // Draw chest panel
    ctx.fillStyle = '#ECF0F1'
    ctx.strokeStyle = '#BDC3C7'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.roundRect(-25, 0, 50, 30, 5)
    ctx.fill()
    ctx.stroke()

    // Draw chest buttons
    ctx.fillStyle = '#3498DB'
    for (let i = 0; i < 3; i++) {
      ctx.beginPath()
      ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2)
      ctx.fill()
    }

    ctx.restore()
  }

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // Check if click is on robot (simple distance check)
    const centerX = canvas.width / 2
    const centerY = canvas.height / 2
    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2)

    if (distance < 100 * scale) {
      onInteraction?.('robot_click', { x, y })
    }
  }

  return (
    <div className={`robot-interface ${className}`}>
      <canvas
        ref={canvasRef}
        width={500}
        height={500}
        onClick={handleCanvasClick}
        className="cursor-pointer"
        style={{
          maxWidth: '100%',
          height: 'auto',
        }}
      />
    </div>
  )
}
