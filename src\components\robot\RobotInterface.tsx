'use client'

import { useEffect, useRef, useState } from 'react'
import { RobotState, AnimationState } from '@/types'

interface RobotInterfaceProps {
  state: AnimationState
  onInteraction?: (type: string, data?: any) => void
  scale?: number
  className?: string
}

export function RobotInterface({ 
  state = 'idle', 
  onInteraction, 
  scale = 0.5,
  className = '' 
}: RobotInterfaceProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameRef = useRef<number>()
  const [robotImages, setRobotImages] = useState<{[key: string]: HTMLImageElement}>({})
  const [imagesLoaded, setImagesLoaded] = useState(false)

  // Robot animation state
  const robotStateRef = useRef({
    currentState: state,
    position: { x: 0, y: 0 },
    scale: scale,
    rotation: 0,
    headBobAmount: 0,
    bodyRotation: 0,
    armRotations: { left: 0, right: 0 },
    currentView: 'front',
    targetView: 'front',
    animationTime: 0,
    isBlinking: false,
    talkPulse: 0,
    dancePhase: 0,
    searchAngle: 0
  })

  // Load robot images
  useEffect(() => {
    const imageUrls = {
      front: '/images/robot_front_view.png',
      side: '/images/robot_side_view.png',
      threeQuarter: '/images/robot_three_quarter_view.png',
      back: '/images/robot_back_view.png',
      top: '/images/robot_top_view.png'
    }

    const loadImages = async () => {
      const images: {[key: string]: HTMLImageElement} = {}
      const loadPromises = Object.entries(imageUrls).map(([key, url]) => {
        return new Promise<void>((resolve, reject) => {
          const img = new Image()
          img.onload = () => {
            images[key] = img
            resolve()
          }
          img.onerror = () => {
            // Create a fallback colored rectangle if image fails to load
            const canvas = document.createElement('canvas')
            canvas.width = 200
            canvas.height = 200
            const ctx = canvas.getContext('2d')
            if (ctx) {
              ctx.fillStyle = '#4285f4'
              ctx.fillRect(0, 0, 200, 200)
              ctx.fillStyle = '#ffffff'
              ctx.font = '16px Arial'
              ctx.textAlign = 'center'
              ctx.fillText('Daswos', 100, 100)
              ctx.fillText('Robot', 100, 120)
            }
            const fallbackImg = new Image()
            fallbackImg.src = canvas.toDataURL()
            images[key] = fallbackImg
            resolve()
          }
          img.src = url
        })
      })

      try {
        await Promise.all(loadPromises)
        setRobotImages(images)
        setImagesLoaded(true)
      } catch (error) {
        console.error('Failed to load robot images:', error)
        setImagesLoaded(true) // Still set to true to show fallback
      }
    }

    loadImages()
  }, [])

  // Update robot state when props change
  useEffect(() => {
    robotStateRef.current.currentState = state
    robotStateRef.current.scale = scale
  }, [state, scale])

  // Animation loop
  useEffect(() => {
    if (!imagesLoaded) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const animate = (timestamp: number) => {
      const robotState = robotStateRef.current
      robotState.animationTime = timestamp

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Update animation based on current state
      updateRobotAnimation(robotState, timestamp)

      // Draw robot
      drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2)

      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [imagesLoaded, robotImages])

  const updateRobotAnimation = (robotState: any, timestamp: number) => {
    const timeInSeconds = timestamp * 0.001

    switch (robotState.currentState) {
      case 'idle':
        robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 5
        robotState.armRotations.left = Math.sin(timeInSeconds) * 0.05
        robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.05
        robotState.targetView = 'front'
        break

      case 'talking':
        robotState.talkPulse = Math.sin(timeInSeconds * 10) * 0.05
        robotState.headBobAmount = Math.sin(timeInSeconds * 10) * 3
        robotState.armRotations.left = Math.sin(timeInSeconds * 8) * 0.2
        robotState.armRotations.right = Math.sin(timeInSeconds * 8 + Math.PI) * 0.2
        robotState.targetView = 'front'
        break

      case 'listening':
        robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 2
        robotState.rotation = Math.sin(timeInSeconds * 2) * 0.1
        robotState.targetView = 'front'
        break

      case 'thinking':
        robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 3
        robotState.rotation = Math.sin(timeInSeconds * 0.5) * 0.2
        robotState.targetView = 'threeQuarter'
        break

      case 'dancing':
        robotState.dancePhase += 0.05
        robotState.headBobAmount = Math.sin(robotState.dancePhase * 2) * 8
        robotState.rotation = Math.sin(robotState.dancePhase) * 0.2
        robotState.armRotations.left = Math.sin(robotState.dancePhase) * 0.4
        robotState.armRotations.right = Math.sin(robotState.dancePhase + Math.PI) * 0.4
        
        // Cycle through views
        const viewCycle = Math.floor(timeInSeconds * 0.5) % 4
        const views = ['front', 'threeQuarter', 'side', 'threeQuarter']
        robotState.targetView = views[viewCycle]
        break

      case 'searching':
        robotState.searchAngle += 0.03
        robotState.rotation = Math.sin(robotState.searchAngle) * 0.3
        robotState.headBobAmount = Math.sin(timeInSeconds * 5) * 3
        robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.5) * 0.2
        robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.5 + Math.PI) * 0.2
        
        // Cycle through views for searching
        const searchViewCycle = Math.floor(timeInSeconds * 0.33) % 3
        const searchViews = ['front', 'threeQuarter', 'side']
        robotState.targetView = searchViews[searchViewCycle]
        break
    }

    // Smooth view transitions
    if (robotState.currentView !== robotState.targetView) {
      robotState.currentView = robotState.targetView
    }
  }

  const drawRobot = (ctx: CanvasRenderingContext2D, robotState: any, centerX: number, centerY: number) => {
    ctx.save()

    // Move to center and apply transformations
    ctx.translate(centerX, centerY + robotState.headBobAmount)
    ctx.scale(robotState.scale, robotState.scale)
    ctx.rotate(robotState.rotation)

    // Apply talk pulse if talking
    if (robotState.currentState === 'talking') {
      ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse)
    }

    // Draw robot image
    const currentImage = robotImages[robotState.currentView] || robotImages['front']
    if (currentImage) {
      ctx.drawImage(
        currentImage,
        -currentImage.width / 2,
        -currentImage.height / 2
      )
    }

    ctx.restore()
  }

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // Check if click is on robot (simple distance check)
    const centerX = canvas.width / 2
    const centerY = canvas.height / 2
    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2)
    
    if (distance < 100 * scale) {
      onInteraction?.('robot_click', { x, y })
    }
  }

  return (
    <div className={`robot-interface ${className}`}>
      <canvas
        ref={canvasRef}
        width={400}
        height={400}
        onClick={handleCanvasClick}
        className="cursor-pointer"
        style={{
          maxWidth: '100%',
          height: 'auto',
        }}
      />
      {!imagesLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading Daswos...</p>
          </div>
        </div>
      )}
    </div>
  )
}
