'use client'

import { useEffect, useRef, useState } from 'react'
import { RobotState, AnimationState } from '@/types'

interface RobotInterfaceProps {
  state: AnimationState
  onInteraction?: (type: string, data?: any) => void
  scale?: number
  className?: string
}

export function RobotInterface({
  state = 'idle',
  onInteraction,
  scale = 0.8,
  className = ''
}: RobotInterfaceProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameRef = useRef<number>()

  // Robot animation state
  const robotStateRef = useRef({
    currentState: state,
    position: { x: 300, y: 300 }, // Start in center
    velocity: { x: 0.8, y: 0.6 }, // Slower, gentler movement
    targetPosition: { x: 300, y: 300 },
    scale: scale * 1.3, // Make robot larger
    rotation: 0,
    baseRotation: 0, // Keep track of base rotation for upright position
    headBobAmount: 0,
    bodyRotation: 0,
    armRotations: { left: 0, right: 0 },
    animationTime: 0,
    isBlinking: false,
    blinkTimer: 0,
    talkPulse: 0,
    dancePhase: 0,
    searchAngle: 0,
    eyeScale: 1,
    mouthScale: 1,
    mouthOpen: 0,
    moveTimer: 0,
    isMoving: true,
    centerPull: 0.002 // Gentle pull toward center
  })

  // No need to load images - we'll draw the robot with canvas primitives

  // Update robot state when props change
  useEffect(() => {
    robotStateRef.current.currentState = state
    robotStateRef.current.scale = scale
  }, [state, scale])

  // Animation loop
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const animate = (timestamp: number) => {
      const robotState = robotStateRef.current
      robotState.animationTime = timestamp

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Update animation based on current state
      updateRobotAnimation(robotState, timestamp)

      // Draw robot at its current position
      drawRobot(ctx, robotState, robotState.position.x, robotState.position.y)

      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  const updateRobotAnimation = (robotState: any, timestamp: number) => {
    const timeInSeconds = timestamp * 0.001
    const canvas = canvasRef.current
    if (!canvas) return

    // Handle free movement around the screen
    robotState.moveTimer += 0.016

    if (robotState.isMoving) {
      // Gentle pull toward center
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2
      const distanceFromCenter = Math.sqrt(
        Math.pow(robotState.position.x - centerX, 2) +
        Math.pow(robotState.position.y - centerY, 2)
      )

      // Apply gentle center pull when far from center
      if (distanceFromCenter > 150) {
        robotState.velocity.x += (centerX - robotState.position.x) * robotState.centerPull
        robotState.velocity.y += (centerY - robotState.position.y) * robotState.centerPull
      }

      // Update position
      robotState.position.x += robotState.velocity.x
      robotState.position.y += robotState.velocity.y

      // Bounce off walls with gentler rotation
      const margin = 100 // Robot size margin
      if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {
        robotState.velocity.x *= -0.8 // Softer bounce
        robotState.baseRotation += 0.05 // Gentle spin when bouncing
      }
      if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {
        robotState.velocity.y *= -0.8 // Softer bounce
        robotState.baseRotation += 0.05 // Gentle spin when bouncing
      }

      // Keep within bounds
      robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x))
      robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y))

      // Randomly change direction occasionally (less frequent)
      if (Math.random() < 0.003) { // 0.3% chance per frame
        robotState.velocity.x = (Math.random() - 0.5) * 2
        robotState.velocity.y = (Math.random() - 0.5) * 2
      }

      // Gradually return to upright position
      robotState.baseRotation *= 0.98 // Slowly reduce rotation
    }

    // Handle blinking
    robotState.blinkTimer += 0.016 // ~60fps
    if (robotState.blinkTimer > 3 + Math.random() * 2) { // Blink every 3-5 seconds
      robotState.isBlinking = true
      robotState.blinkTimer = 0
    }
    if (robotState.isBlinking) {
      robotState.eyeScale = Math.max(0.1, 1 - (robotState.blinkTimer * 10))
      if (robotState.blinkTimer > 0.2) {
        robotState.isBlinking = false
        robotState.eyeScale = 1
      }
    }

    switch (robotState.currentState) {
      case 'idle':
        robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8
        robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1
        robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1
        robotState.mouthOpen = 0
        robotState.isMoving = true // Allow movement when idle
        robotState.rotation = robotState.baseRotation + Math.sin(timeInSeconds * 1.5) * 0.05 // Gentle sway
        break

      case 'talking':
        robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1
        robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12
        robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3
        robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3
        robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8
        robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1
        robotState.isMoving = false // Stop moving when talking
        robotState.rotation = robotState.baseRotation // Stay upright when talking
        break

      case 'listening':
        robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5
        robotState.rotation = robotState.baseRotation + Math.sin(timeInSeconds * 2) * 0.1 // Gentle tilt
        robotState.mouthOpen = 0.2
        robotState.eyeScale = 1.2 // Wider eyes when listening
        robotState.isMoving = false // Stop moving when listening
        break

      case 'thinking':
        robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6
        robotState.rotation = robotState.baseRotation + Math.sin(timeInSeconds * 0.8) * 0.15 // Gentle thinking tilt
        robotState.mouthOpen = 0.1
        robotState.eyeScale = 0.8 // Squinted eyes when thinking
        robotState.isMoving = false // Stop moving when thinking
        break

      case 'dancing':
        robotState.dancePhase += 0.08
        robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15
        robotState.baseRotation += 0.03 // Continuous spinning while dancing
        robotState.rotation = robotState.baseRotation + Math.sin(robotState.dancePhase) * 0.2
        robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6
        robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6
        robotState.mouthOpen = 0.6 // Happy expression
        robotState.eyeScale = 1.3 // Excited eyes
        // Move faster while dancing
        robotState.velocity.x *= 1.05
        robotState.velocity.y *= 1.05
        robotState.isMoving = true
        break

      case 'searching':
        robotState.searchAngle += 0.04
        robotState.rotation = robotState.baseRotation + Math.sin(robotState.searchAngle) * 0.3
        robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8
        robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25
        robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25
        robotState.mouthOpen = 0.3
        robotState.eyeScale = 1.1
        robotState.isMoving = true
        break
    }
  }

  const drawRobot = (ctx: CanvasRenderingContext2D, robotState: any, centerX: number, centerY: number) => {
    ctx.save()

    // Move to center and apply transformations
    ctx.translate(centerX, centerY + robotState.headBobAmount)
    ctx.scale(robotState.scale, robotState.scale)
    ctx.rotate(robotState.rotation)

    // Apply talk pulse if talking
    if (robotState.currentState === 'talking') {
      ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse)
    }

    // Robot colors - matching your robot images
    const headColor = '#5B9BD5'  // Blue head like in your images
    const bodyColor = '#FFFFFF'  // White body
    const eyeColor = '#2C3E50'   // Dark eyes
    const mouthColor = '#FF6B6B' // Red mouth
    const armColor = '#4A4A4A'   // Dark gray arms

    // Draw robot body (main torso) - more rounded like your images
    ctx.fillStyle = bodyColor
    ctx.strokeStyle = '#E0E0E0'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.ellipse(0, 10, 55, 70, 0, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()

    // Draw robot head - more rounded and friendly
    ctx.fillStyle = headColor
    ctx.strokeStyle = '#4A90E2'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.ellipse(0, -50, 45, 40, 0, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()

    // Draw eyes - larger and more expressive like your robot
    ctx.fillStyle = eyeColor
    const eyeY = -55
    const eyeSize = 6 * robotState.eyeScale

    // Left eye
    ctx.beginPath()
    ctx.ellipse(-15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2)
    ctx.fill()

    // Right eye
    ctx.beginPath()
    ctx.ellipse(15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2)
    ctx.fill()

    // Add eye highlights for more life
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(-15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2)
    ctx.fill()
    ctx.beginPath()
    ctx.ellipse(15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2)
    ctx.fill()

    // Draw mouth - more curved and friendly
    ctx.strokeStyle = mouthColor
    ctx.lineWidth = 3
    ctx.beginPath()
    if (robotState.mouthOpen > 0) {
      // Open mouth (oval)
      ctx.ellipse(0, -35, 10, 4 + robotState.mouthOpen * 8, 0, 0, Math.PI * 2)
      ctx.stroke()
    } else {
      // Closed mouth (curved line)
      ctx.arc(0, -30, 8, 0.2, Math.PI - 0.2)
      ctx.stroke()
    }

    // Draw left arm
    ctx.save()
    ctx.translate(-70, 0)
    ctx.rotate(robotState.armRotations.left)
    ctx.fillStyle = armColor
    ctx.strokeStyle = '#2C3E50'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.roundRect(-8, -25, 16, 50, 8)
    ctx.fill()
    ctx.stroke()
    ctx.restore()

    // Draw right arm
    ctx.save()
    ctx.translate(70, 0)
    ctx.rotate(robotState.armRotations.right)
    ctx.fillStyle = armColor
    ctx.strokeStyle = '#2C3E50'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.roundRect(-8, -25, 16, 50, 8)
    ctx.fill()
    ctx.stroke()
    ctx.restore()

    // Draw antenna
    ctx.strokeStyle = '#7F8C8D'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.moveTo(0, -105)
    ctx.lineTo(0, -120)
    ctx.stroke()

    // Antenna tip
    ctx.fillStyle = '#E74C3C'
    ctx.beginPath()
    ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2)
    ctx.fill()

    // Draw chest panel
    ctx.fillStyle = '#ECF0F1'
    ctx.strokeStyle = '#BDC3C7'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.roundRect(-25, 0, 50, 30, 5)
    ctx.fill()
    ctx.stroke()

    // Draw chest buttons
    ctx.fillStyle = '#3498DB'
    for (let i = 0; i < 3; i++) {
      ctx.beginPath()
      ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2)
      ctx.fill()
    }

    ctx.restore()
  }

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // Check if click is on robot (simple distance check)
    const centerX = canvas.width / 2
    const centerY = canvas.height / 2
    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2)

    if (distance < 100 * scale) {
      onInteraction?.('robot_click', { x, y })
    }
  }

  return (
    <div className={`robot-interface ${className} relative`}>
      <canvas
        ref={canvasRef}
        width={600}
        height={600}
        onClick={handleCanvasClick}
        className="cursor-pointer w-full h-full"
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
        }}
      />
    </div>
  )
}
