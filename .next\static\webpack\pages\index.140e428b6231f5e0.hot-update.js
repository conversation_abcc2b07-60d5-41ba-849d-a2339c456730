"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 250,\n            y: 250\n        },\n        velocity: {\n            x: 1,\n            y: 0.5\n        },\n        targetPosition: {\n            x: 250,\n            y: 250\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls\n            const margin = 80 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally\n            if (Math.random() < 0.005) {\n                robotState.velocity.x = (Math.random() - 0.5) * 3;\n                robotState.velocity.y = (Math.random() - 0.5) * 3;\n                robotState.rotation += (Math.random() - 0.5) * 0.5;\n            }\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation += 0.05 // Continuous spinning while dancing\n                ;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.1;\n                robotState.velocity.y *= 1.1;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors\n        const headColor = \"#4A90E2\";\n        const bodyColor = \"#F5F5F5\";\n        const eyeColor = \"#2C3E50\";\n        const mouthColor = \"#E74C3C\";\n        const armColor = \"#34495E\";\n        // Draw robot body (main torso)\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#2980B9\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes\n        ctx.fillStyle = eyeColor;\n        const eyeY = -70;\n        const eyeSize = 8 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 4;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (line)\n            ctx.moveTo(-12, -45);\n            ctx.lineTo(12, -45);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 500,\n            height: 500,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 340,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});