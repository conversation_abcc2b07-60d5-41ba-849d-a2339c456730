import { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '@/lib/supabase'
import { scrypt, randomBytes } from 'crypto'
import { promisify } from 'util'

const scryptAsync = promisify(scrypt)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' })
    }

    // TEMPORARY: Hardcoded test user for development
    if (username === 'test' && password === 'test') {
      const testUser = {
        id: 'test-user-id',
        username: 'test',
        email: '<EMAIL>',
        fullName: 'Test User',
        isSeller: false,
        isAdmin: false,
        hasSubscription: true,
        familyOwnerId: null,
        parentAccountId: null,
        isChildAccount: false,
        identityVerified: true,
        identityVerificationStatus: 'verified',
        trustScore: 85,
        verificationStatus: 'verified',
        tier: 'safesphere'
      }

      return res.status(200).json({
        success: true,
        user: testUser,
        message: 'Login successful (test user)'
      })
    }

    // Query your users table - check both username and email (case-insensitive)
    const { data: users, error } = await supabase
      .from('users')
      .select('id, username, email, password, fullName, isSeller, isAdmin, hasSubscription, familyOwnerId, parentAccountId, isChildAccount, identityVerified, identityVerificationStatus')
      .or(`username.ilike.${username},email.ilike.${username}`)

    if (error || !users || users.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' })
    }

    const user = users[0]

    const isPasswordValid = await verifyPassword(password, user)

    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' })
    }

    // Remove sensitive data before sending response
    const { password: _, password_hash: __, ...safeUser } = user as any

    res.status(200).json({
      success: true,
      user: safeUser,
      message: 'Login successful'
    })

  } catch (error) {
    console.error('Login API error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}

// Password verification function - matches your existing server/auth.ts implementation
async function verifyPassword(inputPassword: string, user: any): Promise<boolean> {
  try {
    if (!user.password || !inputPassword) {
      return false
    }

    // Parse the stored password hash (format: hash.salt)
    const [storedHash, salt] = user.password.split('.')

    if (!storedHash || !salt) {
      console.error('Invalid password format in database')
      return false
    }

    // Hash the input password with the stored salt using scrypt
    const hashedInput = await scryptAsync(inputPassword, salt, 64) as Buffer
    const hashedInputHex = hashedInput.toString('hex')

    // Compare the hashes
    return hashedInputHex === storedHash
  } catch (error) {
    console.error('Password verification error:', error)
    return false
  }
}

// Helper function to hash passwords (for reference - matches your server/auth.ts)
async function hashPassword(password: string): Promise<string> {
  try {
    const salt = randomBytes(16).toString('hex')
    const hashedPassword = await scryptAsync(password, salt, 64) as Buffer
    return `${hashedPassword.toString('hex')}.${salt}`
  } catch (error) {
    console.error('Password hashing error:', error)
    throw new Error('Failed to hash password')
  }
}

// Alternative: If you want to integrate with your existing authentication system
async function callExistingAuthSystem(username: string, password: string): Promise<any> {
  // If you have an existing authentication microservice or API
  // you can call it here instead of implementing password verification

  try {
    // Example: Call your existing auth service
    // const response = await fetch('https://your-auth-service.com/login', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ username, password })
    // })
    //
    // if (!response.ok) {
    //   return null
    // }
    //
    // return await response.json()

    return null // Placeholder
  } catch (error) {
    console.error('External auth system error:', error)
    return null
  }
}
