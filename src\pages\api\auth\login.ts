import { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '@/lib/supabase'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' })
    }

    // Query your users table - check both username and email (case-insensitive)
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .or(`username.ilike.${username},email.ilike.${username}`)

    if (error || !users || users.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' })
    }

    const user = users[0]

    // TODO: Implement proper password verification
    // This is where you need to verify the password against your stored hash
    // 
    // Common approaches:
    // 1. If you store plain text passwords (NOT RECOMMENDED):
    //    if (user.password !== password) { return error }
    //
    // 2. If you use bcrypt:
    //    const bcrypt = require('bcrypt')
    //    const isValid = await bcrypt.compare(password, user.password_hash)
    //    if (!isValid) { return error }
    //
    // 3. If you use another hashing method, implement accordingly
    //
    // For now, we'll create a placeholder that you can customize:

    const isPasswordValid = await verifyPassword(password, user)
    
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' })
    }

    // Remove sensitive data before sending response
    const { password: _, password_hash: __, ...safeUser } = user as any

    res.status(200).json({
      success: true,
      user: safeUser,
      message: 'Login successful'
    })

  } catch (error) {
    console.error('Login API error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}

// Password verification function - CUSTOMIZE THIS FOR YOUR SYSTEM
async function verifyPassword(inputPassword: string, user: any): Promise<boolean> {
  // IMPORTANT: Replace this with your actual password verification logic
  
  // Option 1: If you have a password field in your users table
  // return user.password === inputPassword // NOT SECURE - only for testing
  
  // Option 2: If you have a password_hash field and use bcrypt
  // const bcrypt = require('bcrypt')
  // return await bcrypt.compare(inputPassword, user.password_hash)
  
  // Option 3: If you have a different authentication system
  // Call your existing authentication service/function here
  
  // Option 4: If passwords are stored in a separate table
  // const { data: authData } = await supabase
  //   .from('user_auth')
  //   .select('password_hash')
  //   .eq('user_id', user.id)
  //   .single()
  // return await bcrypt.compare(inputPassword, authData.password_hash)

  // TEMPORARY: For testing purposes, we'll return true
  // REMOVE THIS IN PRODUCTION and implement proper password verification
  console.warn('WARNING: Using placeholder password verification. Implement proper verification!')
  
  // You can temporarily check if the user exists to test the flow
  return true // CHANGE THIS TO ACTUAL PASSWORD VERIFICATION
}

// Alternative: If you want to integrate with your existing authentication system
async function callExistingAuthSystem(username: string, password: string): Promise<any> {
  // If you have an existing authentication microservice or API
  // you can call it here instead of implementing password verification
  
  try {
    // Example: Call your existing auth service
    // const response = await fetch('https://your-auth-service.com/login', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ username, password })
    // })
    // 
    // if (!response.ok) {
    //   return null
    // }
    // 
    // return await response.json()
    
    return null // Placeholder
  } catch (error) {
    console.error('External auth system error:', error)
    return null
  }
}
