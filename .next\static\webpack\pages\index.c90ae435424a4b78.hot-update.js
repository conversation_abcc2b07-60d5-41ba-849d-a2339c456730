"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"./node_modules/@tanstack/react-query/build/modern/index.js\");\n/* harmony import */ var _components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/LoginForm */ \"./src/components/auth/LoginForm.tsx\");\n/* harmony import */ var _components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/RegisterForm */ \"./src/components/auth/RegisterForm.tsx\");\n/* harmony import */ var _components_robot_RobotInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/robot/RobotInterface */ \"./src/components/robot/RobotInterface.tsx\");\n/* harmony import */ var _components_voice_VoiceChat__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/voice/VoiceChat */ \"./src/components/voice/VoiceChat.tsx\");\n/* harmony import */ var _components_cart_ShoppingCart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/cart/ShoppingCart */ \"./src/components/cart/ShoppingCart.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/toaster */ \"./src/components/ui/toaster.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,ShoppingBag,User!=!lucide-react */ \"__barrel_optimize__?names=LogOut,ShoppingBag,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.QueryClient();\nfunction DaswosApp() {\n    var _user_user_metadata;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [authMode, setAuthMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"login\");\n    const [showAuth, setShowAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cartItems, setCartItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [robotState, setRobotState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [voiceSettings, setVoiceSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enabled: true,\n        autoSpeak: true,\n        voice: \"nova\",\n        speed: 1,\n        volume: 0.8\n    });\n    const [showCart, setShowCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatLoading, setIsChatLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Initialize app\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeApp();\n    }, []);\n    const initializeApp = async ()=>{\n        try {\n            const currentUser = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.getCurrentUser)();\n            if (currentUser) {\n                var _currentUser_user_metadata;\n                // Fetch user data from your database\n                setUser(currentUser);\n                await loadCartItems(currentUser.id);\n                // Welcome message\n                setMessages([\n                    {\n                        id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                        role: \"assistant\",\n                        content: \"Hello \".concat(((_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.full_name) || \"there\", \"! I'm Daswos, your AI shopping assistant. How can I help you find products today?\"),\n                        timestamp: new Date()\n                    }\n                ]);\n            } else {\n                setShowAuth(true);\n            }\n        } catch (error) {\n            console.error(\"Failed to initialize app:\", error);\n            setShowAuth(true);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadCartItems = async (userId)=>{\n        try {\n            const { data, error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.getCartItems)(userId);\n            if (!error && data) {\n                setCartItems(data);\n            }\n        } catch (error) {\n            console.error(\"Failed to load cart items:\", error);\n        }\n    };\n    const handleAuthSuccess = (authUser)=>{\n        setUser(authUser);\n        setShowAuth(false);\n        // Welcome message for new users\n        setMessages([\n            {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                role: \"assistant\",\n                content: \"Welcome to DasWos AI! I'm your personal shopping assistant. I can help you find products, add them to your cart, and answer questions about our marketplace. What would you like to shop for today?\",\n                timestamp: new Date()\n            }\n        ]);\n        toast({\n            title: \"Welcome!\",\n            description: \"You can now start chatting with Daswos.\"\n        });\n    };\n    const handleSendMessage = async function(message) {\n        let isVoice = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!user) {\n            toast({\n                title: \"Please sign in\",\n                description: \"You need to be signed in to chat with Daswos.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Add user message\n        const userMessage = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n            role: \"user\",\n            content: message,\n            timestamp: new Date(),\n            metadata: isVoice ? {\n                audioUrl: undefined\n            } : undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setRobotState(\"thinking\");\n        setIsChatLoading(true);\n        try {\n            // Send to chat API\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message,\n                    chatHistory: messages,\n                    userId: user.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            // Add assistant message\n            const assistantMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                role: \"assistant\",\n                content: data.message,\n                timestamp: new Date(),\n                metadata: {\n                    recommendations: data.recommendations,\n                    actions: data.actions\n                }\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            setRobotState(\"talking\");\n            // Handle actions\n            if (data.actions) {\n                for (const action of data.actions){\n                    if (action.type === \"add_to_cart\") {\n                        await loadCartItems(user.id);\n                    }\n                }\n            }\n            // Return to idle after talking\n            setTimeout(()=>{\n                setRobotState(\"idle\");\n            }, 3000);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                role: \"assistant\",\n                content: \"I'm sorry, I'm having trouble right now. Please try again in a moment.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            setRobotState(\"idle\");\n            toast({\n                title: \"Chat error\",\n                description: \"Failed to get response from Daswos. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsChatLoading(false);\n        }\n    };\n    const handleUpdateCartQuantity = async (itemId, quantity)=>{\n        try {\n            const { error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.updateCartItemQuantity)(itemId, quantity);\n            if (!error && user) {\n                await loadCartItems(user.id);\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to update cart item.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRemoveFromCart = async (itemId)=>{\n        try {\n            const { error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.removeFromCart)(itemId);\n            if (!error && user) {\n                await loadCartItems(user.id);\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to remove item from cart.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRobotInteraction = (type, data)=>{\n        if (type === \"robot_click\") {\n            setRobotState(\"dancing\");\n            setTimeout(()=>setRobotState(\"idle\"), 2000);\n        }\n    };\n    const handleLogout = async ()=>{\n        setUser(null);\n        setMessages([]);\n        setCartItems([]);\n        setShowAuth(true);\n        toast({\n            title: \"Logged out\",\n            description: \"You have been successfully logged out.\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading DasWos AI...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this);\n    }\n    if (showAuth) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 p-4\",\n            children: authMode === \"login\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__.LoginForm, {\n                onSuccess: handleAuthSuccess,\n                onSwitchToRegister: ()=>setAuthMode(\"register\")\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 259,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_3__.RegisterForm, {\n                onSuccess: handleAuthSuccess,\n                onSwitchToLogin: ()=>setAuthMode(\"login\")\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 264,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-primary\",\n                                    children: \"DasWos AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"AI Shopping Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowCart(!showCart),\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ShoppingBag, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Cart\",\n                                        cartItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: cartItems.reduce((sum, item)=>sum + item.quantity, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__.User, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : user.email)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: handleLogout,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LogOut, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[calc(100vh-200px)]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_voice_VoiceChat__WEBPACK_IMPORTED_MODULE_5__.VoiceChat, {\n                                messages: messages,\n                                onSendMessage: handleSendMessage,\n                                onVoiceSettingsChange: setVoiceSettings,\n                                isLoading: isChatLoading,\n                                className: \"h-full min-h-[500px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 flex items-center justify-center relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_robot_RobotInterface__WEBPACK_IMPORTED_MODULE_4__.RobotInterface, {\n                                state: robotState,\n                                onInteraction: handleRobotInteraction,\n                                scale: 0.6,\n                                className: \"absolute inset-0 w-full h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cart_ShoppingCart__WEBPACK_IMPORTED_MODULE_6__.ShoppingCart, {\n                                items: cartItems,\n                                onUpdateQuantity: handleUpdateCartQuantity,\n                                onRemoveItem: handleRemoveFromCart,\n                                onCheckout: ()=>{\n                                    toast({\n                                        title: \"Checkout\",\n                                        description: \"Checkout functionality coming soon!\"\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_8__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(DaswosApp, \"h17jaHjzHVVdu9AratrO5SqIzJ8=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = DaswosApp;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DaswosApp, {}, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"DaswosApp\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});