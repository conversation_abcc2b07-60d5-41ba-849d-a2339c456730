import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price / 100) // Assuming price is in cents
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function calculateTrustLevel(trustScore: number): {
  level: 'low' | 'medium' | 'high' | 'excellent'
  color: string
  description: string
} {
  if (trustScore >= 90) {
    return {
      level: 'excellent',
      color: 'text-green-600',
      description: 'Excellent trust score'
    }
  } else if (trustScore >= 75) {
    return {
      level: 'high',
      color: 'text-blue-600',
      description: 'High trust score'
    }
  } else if (trustScore >= 50) {
    return {
      level: 'medium',
      color: 'text-yellow-600',
      description: 'Medium trust score'
    }
  } else {
    return {
      level: 'low',
      color: 'text-red-600',
      description: 'Low trust score'
    }
  }
}

export function getSphereColor(sphere: 'safesphere' | 'opensphere'): string {
  return sphere === 'safesphere' ? 'text-green-600' : 'text-blue-600'
}

export function getSphereLabel(sphere: 'safesphere' | 'opensphere'): string {
  return sphere === 'safesphere' ? 'SafeSphere' : 'OpenSphere'
}

// Audio utilities
export function createAudioContext(): AudioContext | null {
  try {
    return new (window.AudioContext || (window as any).webkitAudioContext)()
  } catch (error) {
    console.error('Web Audio API not supported:', error)
    return null
  }
}

export function requestMicrophonePermission(): Promise<MediaStream> {
  return navigator.mediaDevices.getUserMedia({ audio: true })
}

// Local storage utilities
export function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') return defaultValue
  
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error)
    return defaultValue
  }
}

export function setToLocalStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error)
  }
}

export function removeFromLocalStorage(key: string): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error(`Error removing from localStorage key "${key}":`, error)
  }
}

// Error handling utilities
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message
  if (typeof error === 'string') return error
  return 'An unknown error occurred'
}

export function logError(error: unknown, context?: string): void {
  const message = getErrorMessage(error)
  console.error(`${context ? `[${context}] ` : ''}${message}`, error)
}

// Animation utilities
export function easeInOutCubic(t: number): number {
  return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
}

export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * factor
}
