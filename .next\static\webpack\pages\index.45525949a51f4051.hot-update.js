"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 250,\n            y: 250\n        },\n        velocity: {\n            x: 1,\n            y: 0.5\n        },\n        targetPosition: {\n            x: 250,\n            y: 250\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation = Math.sin(robotState.dancePhase * 1.5) * 0.4;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors\n        const headColor = \"#4A90E2\";\n        const bodyColor = \"#F5F5F5\";\n        const eyeColor = \"#2C3E50\";\n        const mouthColor = \"#E74C3C\";\n        const armColor = \"#34495E\";\n        // Draw robot body (main torso)\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#2980B9\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes\n        ctx.fillStyle = eyeColor;\n        const eyeY = -70;\n        const eyeSize = 8 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 4;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (line)\n            ctx.moveTo(-12, -45);\n            ctx.lineTo(12, -45);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 500,\n            height: 500,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});