"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 250,\n            y: 250\n        },\n        velocity: {\n            x: 1,\n            y: 0.5\n        },\n        targetPosition: {\n            x: 250,\n            y: 250\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot at its current position\n            drawRobot(ctx, robotState, robotState.position.x, robotState.position.y);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls\n            const margin = 80 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally\n            if (Math.random() < 0.005) {\n                robotState.velocity.x = (Math.random() - 0.5) * 3;\n                robotState.velocity.y = (Math.random() - 0.5) * 3;\n                robotState.rotation += (Math.random() - 0.5) * 0.5;\n            }\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation += 0.05 // Continuous spinning while dancing\n                ;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.1;\n                robotState.velocity.y *= 1.1;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors - matching your robot images\n        const headColor = \"#5B9BD5\" // Blue head like in your images\n        ;\n        const bodyColor = \"#FFFFFF\" // White body\n        ;\n        const eyeColor = \"#2C3E50\" // Dark eyes\n        ;\n        const mouthColor = \"#FF6B6B\" // Red mouth\n        ;\n        const armColor = \"#4A4A4A\" // Dark gray arms\n        ;\n        // Draw robot body (main torso) - more rounded like your images\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#E0E0E0\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, 10, 55, 70, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head - more rounded and friendly\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#4A90E2\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, -50, 45, 40, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes - larger and more expressive like your robot\n        ctx.fillStyle = eyeColor;\n        const eyeY = -55;\n        const eyeSize = 6 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Add eye highlights for more life\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth - more curved and friendly\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -35, 10, 4 + robotState.mouthOpen * 8, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (curved line)\n            ctx.arc(0, -30, 8, 0.2, Math.PI - 0.2);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 500,\n            height: 500,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});