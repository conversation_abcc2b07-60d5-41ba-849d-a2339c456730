# Authentication Setup Guide

## ✅ Current Status

Your DasWos AI app now supports **username or email** login, just like your existing app! Here's what's been implemented:

### 🔧 What's Working:
- ✅ Login form accepts username OR email
- ✅ Case-insensitive username/email lookup
- ✅ Integration with your existing users table
- ✅ Session management
- ✅ User data retrieval from database

### ⚠️ What Needs Configuration:
- **Password Verification** - You need to configure how passwords are verified

## 🔑 Password Verification Setup

The authentication system is ready, but you need to configure password verification in:
**`src/pages/api/auth/login.ts`**

### Option 1: If you store password hashes (RECOMMENDED)

If your users table has a `password_hash` field with bcrypt hashes:

```typescript
// Install bcrypt first: npm install bcrypt @types/bcrypt

async function verifyPassword(inputPassword: string, user: any): Promise<boolean> {
  const bcrypt = require('bcrypt')
  return await bcrypt.compare(inputPassword, user.password_hash)
}
```

### Option 2: If you have plain text passwords (NOT SECURE)

**Only for testing - migrate to hashed passwords ASAP:**

```typescript
async function verifyPassword(inputPassword: string, user: any): Promise<boolean> {
  return user.password === inputPassword
}
```

### Option 3: If you have a separate auth table

If passwords are stored in a different table:

```typescript
async function verifyPassword(inputPassword: string, user: any): Promise<boolean> {
  const { data: authData } = await supabase
    .from('user_auth') // Your auth table name
    .select('password_hash')
    .eq('user_id', user.id)
    .single()
  
  const bcrypt = require('bcrypt')
  return await bcrypt.compare(inputPassword, authData.password_hash)
}
```

### Option 4: If you have an existing auth service

If you have an existing authentication API:

```typescript
async function verifyPassword(inputPassword: string, user: any): Promise<boolean> {
  const response = await fetch('https://your-auth-service.com/verify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      username: user.username, 
      password: inputPassword 
    })
  })
  
  return response.ok
}
```

## 🗄️ Database Schema Questions

To help you configure authentication, I need to know:

1. **How are passwords stored in your database?**
   - [ ] Hashed with bcrypt
   - [ ] Hashed with another method (which one?)
   - [ ] Plain text (needs to be changed!)
   - [ ] In a separate table

2. **What's your users table structure?**
   ```sql
   -- Please share your actual users table schema
   -- For example:
   CREATE TABLE users (
     id UUID PRIMARY KEY,
     username TEXT UNIQUE,
     email TEXT UNIQUE,
     password_hash TEXT,  -- or password, or other field name?
     -- ... other fields
   );
   ```

3. **Do you have an existing authentication API?**
   - [ ] Yes, I have an auth service at: `_____________`
   - [ ] No, passwords are verified directly in the database

## 🚀 Quick Test Setup

For immediate testing, the current setup will accept any password (with a warning). To test:

1. **Add your API keys** to `.env.local`
2. **Start the app**: `npm run dev`
3. **Try logging in** with any username from your database
4. **Any password will work** (temporarily)

## 🔒 Security Recommendations

### Immediate (Required):
1. **Configure proper password verification** (see options above)
2. **Use HTTPS in production**
3. **Set secure session management**

### Soon (Recommended):
1. **Migrate to password hashes** if using plain text
2. **Add rate limiting** to prevent brute force attacks
3. **Add password strength requirements**
4. **Implement proper session tokens** instead of localStorage

### Later (Optional):
1. **Add two-factor authentication**
2. **Add password reset functionality**
3. **Add account lockout after failed attempts**

## 📝 Configuration Steps

1. **Identify your password storage method** (see questions above)
2. **Edit** `src/pages/api/auth/login.ts`
3. **Replace the `verifyPassword` function** with your method
4. **Test login** with real credentials
5. **Remove the warning message** once working

## 🧪 Testing Your Setup

Once configured, test with:

```bash
# 1. Start the app
npm run dev

# 2. Open http://localhost:3000

# 3. Try logging in with:
# - Your actual username + password
# - Your actual email + password
# - Wrong credentials (should fail)
```

## ❓ Need Help?

If you're unsure about your password storage method:

1. **Check your existing app's code** for authentication logic
2. **Look at your database** - what fields exist in the users table?
3. **Share your users table schema** and I can help configure it

## 🎯 Next Steps

Once authentication is working:
1. ✅ Users can log in with existing credentials
2. ✅ AI chat will work with user context
3. ✅ Shopping cart will be user-specific
4. ✅ All features will be fully functional

The app is ready - just need to configure password verification! 🚀
