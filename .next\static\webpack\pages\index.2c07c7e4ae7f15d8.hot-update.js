"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 300,\n            y: 300\n        },\n        velocity: {\n            x: 0.8,\n            y: 0.6\n        },\n        targetPosition: {\n            x: 300,\n            y: 300\n        },\n        scale: scale * 1.3,\n        rotation: 0,\n        baseRotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true,\n        centerPull: 0.002 // Gentle pull toward center\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot at its current position\n            drawRobot(ctx, robotState, robotState.position.x, robotState.position.y);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Gentle pull toward center\n            const centerX = canvas.width / 2;\n            const centerY = canvas.height / 2;\n            const distanceFromCenter = Math.sqrt(Math.pow(robotState.position.x - centerX, 2) + Math.pow(robotState.position.y - centerY, 2));\n            // Apply gentle center pull when far from center\n            if (distanceFromCenter > 150) {\n                robotState.velocity.x += (centerX - robotState.position.x) * robotState.centerPull;\n                robotState.velocity.y += (centerY - robotState.position.y) * robotState.centerPull;\n            }\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls with gentler rotation\n            const margin = 100 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -0.8 // Softer bounce\n                ;\n                robotState.baseRotation += 0.05 // Gentle spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -0.8 // Softer bounce\n                ;\n                robotState.baseRotation += 0.05 // Gentle spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally (less frequent)\n            if (Math.random() < 0.003) {\n                robotState.velocity.x = (Math.random() - 0.5) * 2;\n                robotState.velocity.y = (Math.random() - 0.5) * 2;\n            }\n            // Gradually return to upright position\n            robotState.baseRotation *= 0.98 // Slowly reduce rotation\n            ;\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                robotState.isMoving = true // Allow movement when idle\n                ;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                robotState.isMoving = false // Stop moving when talking\n                ;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                robotState.isMoving = false // Stop moving when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                robotState.isMoving = false // Stop moving when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation += 0.05 // Continuous spinning while dancing\n                ;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.1;\n                robotState.velocity.y *= 1.1;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors - matching your robot images\n        const headColor = \"#5B9BD5\" // Blue head like in your images\n        ;\n        const bodyColor = \"#FFFFFF\" // White body\n        ;\n        const eyeColor = \"#2C3E50\" // Dark eyes\n        ;\n        const mouthColor = \"#FF6B6B\" // Red mouth\n        ;\n        const armColor = \"#4A4A4A\" // Dark gray arms\n        ;\n        // Draw robot body (main torso) - more rounded like your images\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#E0E0E0\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, 10, 55, 70, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head - more rounded and friendly\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#4A90E2\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, -50, 45, 40, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes - larger and more expressive like your robot\n        ctx.fillStyle = eyeColor;\n        const eyeY = -55;\n        const eyeSize = 6 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Add eye highlights for more life\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth - more curved and friendly\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -35, 10, 4 + robotState.mouthOpen * 8, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (curved line)\n            ctx.arc(0, -30, 8, 0.2, Math.PI - 0.2);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className, \" relative\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 600,\n            height: 600,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer w-full h-full\",\n            style: {\n                maxWidth: \"100%\",\n                maxHeight: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});