"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"./node_modules/@tanstack/react-query/build/modern/index.js\");\n/* harmony import */ var _components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/LoginForm */ \"./src/components/auth/LoginForm.tsx\");\n/* harmony import */ var _components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/RegisterForm */ \"./src/components/auth/RegisterForm.tsx\");\n/* harmony import */ var _components_robot_RobotInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/robot/RobotInterface */ \"./src/components/robot/RobotInterface.tsx\");\n/* harmony import */ var _components_voice_VoiceChat__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/voice/VoiceChat */ \"./src/components/voice/VoiceChat.tsx\");\n/* harmony import */ var _components_cart_ShoppingCart__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/cart/ShoppingCart */ \"./src/components/cart/ShoppingCart.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/toaster */ \"./src/components/ui/toaster.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,ShoppingBag,User!=!lucide-react */ \"__barrel_optimize__?names=LogOut,ShoppingBag,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.QueryClient();\nfunction DaswosApp() {\n    var _user_user_metadata;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [authMode, setAuthMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"login\");\n    const [showAuth, setShowAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cartItems, setCartItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [robotState, setRobotState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [voiceSettings, setVoiceSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enabled: true,\n        autoSpeak: true,\n        voice: \"nova\",\n        speed: 1,\n        volume: 0.8\n    });\n    const [showCart, setShowCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatLoading, setIsChatLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Initialize app\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeApp();\n    }, []);\n    const initializeApp = async ()=>{\n        try {\n            const currentUser = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.getCurrentUser)();\n            if (currentUser) {\n                var _currentUser_user_metadata;\n                // Fetch user data from your database\n                setUser(currentUser);\n                await loadCartItems(currentUser.id);\n                // Welcome message\n                setMessages([\n                    {\n                        id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                        role: \"assistant\",\n                        content: \"Hello \".concat(((_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.full_name) || \"there\", \"! I'm Daswos, your AI shopping assistant. How can I help you find products today?\"),\n                        timestamp: new Date()\n                    }\n                ]);\n            } else {\n                setShowAuth(true);\n            }\n        } catch (error) {\n            console.error(\"Failed to initialize app:\", error);\n            setShowAuth(true);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadCartItems = async (userId)=>{\n        try {\n            const { data, error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.getCartItems)(userId);\n            if (!error && data) {\n                setCartItems(data);\n            }\n        } catch (error) {\n            console.error(\"Failed to load cart items:\", error);\n        }\n    };\n    const handleAuthSuccess = (authUser)=>{\n        setUser(authUser);\n        setShowAuth(false);\n        // Welcome message for new users\n        setMessages([\n            {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                role: \"assistant\",\n                content: \"Welcome to DasWos AI! I'm your personal shopping assistant. I can help you find products, add them to your cart, and answer questions about our marketplace. What would you like to shop for today?\",\n                timestamp: new Date()\n            }\n        ]);\n        toast({\n            title: \"Welcome!\",\n            description: \"You can now start chatting with Daswos.\"\n        });\n    };\n    const handleSendMessage = async function(message) {\n        let isVoice = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!user) {\n            toast({\n                title: \"Please sign in\",\n                description: \"You need to be signed in to chat with Daswos.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Add user message\n        const userMessage = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n            role: \"user\",\n            content: message,\n            timestamp: new Date(),\n            metadata: isVoice ? {\n                audioUrl: undefined\n            } : undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setRobotState(\"thinking\");\n        setIsChatLoading(true);\n        try {\n            // Send to chat API\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message,\n                    chatHistory: messages,\n                    userId: user.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            // Add assistant message\n            const assistantMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                role: \"assistant\",\n                content: data.message,\n                timestamp: new Date(),\n                metadata: {\n                    recommendations: data.recommendations,\n                    actions: data.actions\n                }\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            setRobotState(\"talking\");\n            // Handle actions\n            if (data.actions) {\n                for (const action of data.actions){\n                    if (action.type === \"add_to_cart\") {\n                        await loadCartItems(user.id);\n                    }\n                }\n            }\n            // Return to idle after talking\n            setTimeout(()=>{\n                setRobotState(\"idle\");\n            }, 3000);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.generateId)(),\n                role: \"assistant\",\n                content: \"I'm sorry, I'm having trouble right now. Please try again in a moment.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n            setRobotState(\"idle\");\n            toast({\n                title: \"Chat error\",\n                description: \"Failed to get response from Daswos. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsChatLoading(false);\n        }\n    };\n    const handleUpdateCartQuantity = async (itemId, quantity)=>{\n        try {\n            const { error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.updateCartItemQuantity)(itemId, quantity);\n            if (!error && user) {\n                await loadCartItems(user.id);\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to update cart item.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRemoveFromCart = async (itemId)=>{\n        try {\n            const { error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_10__.removeFromCart)(itemId);\n            if (!error && user) {\n                await loadCartItems(user.id);\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to remove item from cart.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRobotInteraction = (type, data)=>{\n        if (type === \"robot_click\") {\n            setRobotState(\"dancing\");\n            setTimeout(()=>setRobotState(\"idle\"), 2000);\n        }\n    };\n    const handleLogout = async ()=>{\n        setUser(null);\n        setMessages([]);\n        setCartItems([]);\n        setShowAuth(true);\n        toast({\n            title: \"Logged out\",\n            description: \"You have been successfully logged out.\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading DasWos AI...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this);\n    }\n    if (showAuth) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 p-4\",\n            children: authMode === \"login\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__.LoginForm, {\n                onSuccess: handleAuthSuccess,\n                onSwitchToRegister: ()=>setAuthMode(\"register\")\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 259,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RegisterForm__WEBPACK_IMPORTED_MODULE_3__.RegisterForm, {\n                onSuccess: handleAuthSuccess,\n                onSwitchToLogin: ()=>setAuthMode(\"login\")\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 264,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-primary\",\n                                    children: \"DasWos AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"AI Shopping Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowCart(!showCart),\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ShoppingBag, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Cart\",\n                                        cartItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                                            children: cartItems.reduce((sum, item)=>sum + item.quantity, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__.User, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : user.email)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: handleLogout,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__.LogOut, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[calc(100vh-200px)]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_voice_VoiceChat__WEBPACK_IMPORTED_MODULE_5__.VoiceChat, {\n                                messages: messages,\n                                onSendMessage: handleSendMessage,\n                                onVoiceSettingsChange: setVoiceSettings,\n                                isLoading: isChatLoading,\n                                className: \"h-full min-h-[500px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_robot_RobotInterface__WEBPACK_IMPORTED_MODULE_4__.RobotInterface, {\n                                    state: robotState,\n                                    onInteraction: handleRobotInteraction,\n                                    scale: 0.8,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cart_ShoppingCart__WEBPACK_IMPORTED_MODULE_6__.ShoppingCart, {\n                                items: cartItems,\n                                onUpdateQuantity: handleUpdateCartQuantity,\n                                onRemoveItem: handleRemoveFromCart,\n                                onCheckout: ()=>{\n                                    toast({\n                                        title: \"Checkout\",\n                                        description: \"Checkout functionality coming soon!\"\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_8__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(DaswosApp, \"h17jaHjzHVVdu9AratrO5SqIzJ8=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = DaswosApp;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DaswosApp, {}, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"DaswosApp\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});