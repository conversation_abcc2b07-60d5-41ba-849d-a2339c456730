"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next";
exports.ids = ["vendor-chunks/next"];
exports.modules = {

/***/ "./node_modules/next/dist/build/templates/helpers.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/templates/helpers.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hoist\", ({\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n}));\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if (\"then\" in module && typeof module.then === \"function\") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === \"function\" && name === \"default\") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/templates/helpers.js\n");

/***/ }),

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"react\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"./node_modules/next/dist/shared/lib/constants.js\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"./node_modules/next/dist/server/get-page-files.js\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"./node_modules/next/dist/server/htmlescape.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _htmlcontextsharedruntime = __webpack_require__(/*! ../shared/lib/html-context.shared-runtime */ \"./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix, buildManifest, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${assetQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props_dangerouslySetInnerHTML, _el_props;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports, assetPrefix, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix, buildManifest, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props_dangerouslySetInnerHTML, _child_props;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy, src, children: scriptChildren, dangerouslySetInnerHTML, ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                ...srcProps,\n                ...scriptProps,\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            });\n        }));\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader, disableOptimizedLoading, crossOrigin } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy, ...scriptProps } = file;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin, nonce, ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = \"\") {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages[\"/_app\"];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = [\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ];\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ _react.default.createElement(\"link\", {\n            \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? \"size-adjust\" : \"\",\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: fontFile,\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${encodeURI(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes(\"-s\") ? \"size-adjust\" : \"\"\n            });\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix, assetQueryString, dynamicImports, crossOrigin, optimizeCss, optimizeFonts } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader } = this.context;\n        const { nonce, crossOrigin } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy, children, dangerouslySetInnerHTML, src, ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var _c_props, _c_props1;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (_c_props = c.props) == null ? void 0 : _c_props.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url })=>{\n                var _c_props_href, _c_props;\n                return c == null ? void 0 : (_c_props = c.props) == null ? void 0 : (_c_props_href = _c_props.href) == null ? void 0 : _c_props_href.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (_c_props1 = c.props) == null ? void 0 : _c_props1.children) {\n                const newProps = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n            return c;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles, ampPath, inAmpMode, hybridAmp, canonicalBase, __NEXT_DATA__, dangerousAsPath, headTags, unstable_runtimeJS, unstable_JsPreload, disableOptimizedLoading, optimizeCss, optimizeFonts, assetPrefix, nextFontManifest } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                let metaTag;\n                if (this.context.strictNextHead) {\n                    metaTag = /*#__PURE__*/ _react.default.createElement(\"meta\", {\n                        name: \"next-head\",\n                        content: \"1\"\n                    });\n                }\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    metaTag && cssPreloads.push(metaTag);\n                    cssPreloads.push(c);\n                } else {\n                    if (c) {\n                        if (metaTag && (c.type !== \"meta\" || !c.props[\"charSet\"])) {\n                            otherHeadElements.push(metaTag);\n                        }\n                        otherHeadElements.push(c);\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type, props } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n        return /*#__PURE__*/ _react.default.createElement(\"head\", getHeadHTMLProps(this.props), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, this.context.strictNextHead ? null : /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }), nextFontLinkTags.preconnect, nextFontLinkTags.preload,  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"./node_modules/next/dist/server/utils.js\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find_props, _children_find, _children_find_props1, _children_find1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__, largePageDataBytes } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix, inAmpMode, buildManifest, unstable_runtimeJS, docComponentsRendered, assetQueryString, disableOptimizedLoading, crossOrigin } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${assetQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${assetQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nfunction Html(props) {\n    const { inAmpMode, docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"react\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n    400: \"Bad Request\",\n    404: \"This page could not be found\",\n    405: \"Method Not Allowed\",\n    500: \"Internal Server Error\"\n};\nfunction _getInitialProps(param) {\n    let { res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: \"100vh\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\"\n    },\n    desc: {\n        lineHeight: \"48px\"\n    },\n    h1: {\n        display: \"inline-block\",\n        margin: \"0 20px 0 0\",\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: \"top\"\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: \"28px\"\n    },\n    wrap: {\n        display: \"inline-block\"\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || \"An unexpected error has occurred\";\n        return /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.error\n        }, /*#__PURE__*/ _react.default.createElement(_head.default, null, /*#__PURE__*/ _react.default.createElement(\"title\", null, statusCode ? statusCode + \": \" + title : \"Application error: a client-side exception has occurred\")), /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.desc\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? \"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\" : \"\")\n            }\n        }), statusCode ? /*#__PURE__*/ _react.default.createElement(\"h1\", {\n            className: \"next-error-h1\",\n            style: styles.h1\n        }, statusCode) : null, /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.wrap\n        }, /*#__PURE__*/ _react.default.createElement(\"h2\", {\n            style: styles.h2\n        }, this.props.title || statusCode ? title : /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, \"Application error: a client-side exception has occurred (see the browser console for more information)\"), \".\"))));\n    }\n}\nError.displayName = \"ErrorPage\";\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n}));\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n} //# sourceMappingURL=amp-mode.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvYW1wLW1vZGUuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILCtDQUE4QztJQUMxQ0ksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLFNBQVNBLFlBQVlDLEtBQUs7SUFDdEIsSUFBSSxFQUFFQyxXQUFXLEtBQUssRUFBRUMsU0FBUyxLQUFLLEVBQUVDLFdBQVcsS0FBSyxFQUFFLEdBQUdILFVBQVUsS0FBSyxJQUFJLENBQUMsSUFBSUE7SUFDckYsT0FBT0MsWUFBWUMsVUFBVUM7QUFDakMsRUFFQSxvQ0FBb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYXN3b3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2FtcC1tb2RlLmpzPzYyMzEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJpc0luQW1wTW9kZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gaXNJbkFtcE1vZGU7XG4gICAgfVxufSk7XG5mdW5jdGlvbiBpc0luQW1wTW9kZShwYXJhbSkge1xuICAgIGxldCB7IGFtcEZpcnN0ID0gZmFsc2UsIGh5YnJpZCA9IGZhbHNlLCBoYXNRdWVyeSA9IGZhbHNlIH0gPSBwYXJhbSA9PT0gdm9pZCAwID8ge30gOiBwYXJhbTtcbiAgICByZXR1cm4gYW1wRmlyc3QgfHwgaHlicmlkICYmIGhhc1F1ZXJ5O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hbXAtbW9kZS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiaXNJbkFtcE1vZGUiLCJwYXJhbSIsImFtcEZpcnN0IiwiaHlicmlkIiwiaGFzUXVlcnkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/amp-mode.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/constants.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/constants.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MODERN_BROWSERSLIST_TARGET: function() {\n        return _modernbrowserslisttarget.default;\n    },\n    COMPILER_NAMES: function() {\n        return COMPILER_NAMES;\n    },\n    INTERNAL_HEADERS: function() {\n        return INTERNAL_HEADERS;\n    },\n    COMPILER_INDEXES: function() {\n        return COMPILER_INDEXES;\n    },\n    PHASE_EXPORT: function() {\n        return PHASE_EXPORT;\n    },\n    PHASE_PRODUCTION_BUILD: function() {\n        return PHASE_PRODUCTION_BUILD;\n    },\n    PHASE_PRODUCTION_SERVER: function() {\n        return PHASE_PRODUCTION_SERVER;\n    },\n    PHASE_DEVELOPMENT_SERVER: function() {\n        return PHASE_DEVELOPMENT_SERVER;\n    },\n    PHASE_TEST: function() {\n        return PHASE_TEST;\n    },\n    PHASE_INFO: function() {\n        return PHASE_INFO;\n    },\n    PAGES_MANIFEST: function() {\n        return PAGES_MANIFEST;\n    },\n    APP_PATHS_MANIFEST: function() {\n        return APP_PATHS_MANIFEST;\n    },\n    APP_PATH_ROUTES_MANIFEST: function() {\n        return APP_PATH_ROUTES_MANIFEST;\n    },\n    BUILD_MANIFEST: function() {\n        return BUILD_MANIFEST;\n    },\n    APP_BUILD_MANIFEST: function() {\n        return APP_BUILD_MANIFEST;\n    },\n    FUNCTIONS_CONFIG_MANIFEST: function() {\n        return FUNCTIONS_CONFIG_MANIFEST;\n    },\n    SUBRESOURCE_INTEGRITY_MANIFEST: function() {\n        return SUBRESOURCE_INTEGRITY_MANIFEST;\n    },\n    NEXT_FONT_MANIFEST: function() {\n        return NEXT_FONT_MANIFEST;\n    },\n    EXPORT_MARKER: function() {\n        return EXPORT_MARKER;\n    },\n    EXPORT_DETAIL: function() {\n        return EXPORT_DETAIL;\n    },\n    PRERENDER_MANIFEST: function() {\n        return PRERENDER_MANIFEST;\n    },\n    ROUTES_MANIFEST: function() {\n        return ROUTES_MANIFEST;\n    },\n    IMAGES_MANIFEST: function() {\n        return IMAGES_MANIFEST;\n    },\n    SERVER_FILES_MANIFEST: function() {\n        return SERVER_FILES_MANIFEST;\n    },\n    DEV_CLIENT_PAGES_MANIFEST: function() {\n        return DEV_CLIENT_PAGES_MANIFEST;\n    },\n    MIDDLEWARE_MANIFEST: function() {\n        return MIDDLEWARE_MANIFEST;\n    },\n    DEV_MIDDLEWARE_MANIFEST: function() {\n        return DEV_MIDDLEWARE_MANIFEST;\n    },\n    REACT_LOADABLE_MANIFEST: function() {\n        return REACT_LOADABLE_MANIFEST;\n    },\n    FONT_MANIFEST: function() {\n        return FONT_MANIFEST;\n    },\n    SERVER_DIRECTORY: function() {\n        return SERVER_DIRECTORY;\n    },\n    CONFIG_FILES: function() {\n        return CONFIG_FILES;\n    },\n    BUILD_ID_FILE: function() {\n        return BUILD_ID_FILE;\n    },\n    BLOCKED_PAGES: function() {\n        return BLOCKED_PAGES;\n    },\n    CLIENT_PUBLIC_FILES_PATH: function() {\n        return CLIENT_PUBLIC_FILES_PATH;\n    },\n    CLIENT_STATIC_FILES_PATH: function() {\n        return CLIENT_STATIC_FILES_PATH;\n    },\n    STRING_LITERAL_DROP_BUNDLE: function() {\n        return STRING_LITERAL_DROP_BUNDLE;\n    },\n    NEXT_BUILTIN_DOCUMENT: function() {\n        return NEXT_BUILTIN_DOCUMENT;\n    },\n    BARREL_OPTIMIZATION_PREFIX: function() {\n        return BARREL_OPTIMIZATION_PREFIX;\n    },\n    CLIENT_REFERENCE_MANIFEST: function() {\n        return CLIENT_REFERENCE_MANIFEST;\n    },\n    SERVER_REFERENCE_MANIFEST: function() {\n        return SERVER_REFERENCE_MANIFEST;\n    },\n    MIDDLEWARE_BUILD_MANIFEST: function() {\n        return MIDDLEWARE_BUILD_MANIFEST;\n    },\n    MIDDLEWARE_REACT_LOADABLE_MANIFEST: function() {\n        return MIDDLEWARE_REACT_LOADABLE_MANIFEST;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_MAIN: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_MAIN;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_MAIN_APP: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_MAIN_APP;\n    },\n    APP_CLIENT_INTERNALS: function() {\n        return APP_CLIENT_INTERNALS;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_AMP: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_AMP;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_WEBPACK: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_WEBPACK;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_POLYFILLS: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_POLYFILLS;\n    },\n    CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL: function() {\n        return CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL;\n    },\n    EDGE_RUNTIME_WEBPACK: function() {\n        return EDGE_RUNTIME_WEBPACK;\n    },\n    STATIC_PROPS_ID: function() {\n        return STATIC_PROPS_ID;\n    },\n    SERVER_PROPS_ID: function() {\n        return SERVER_PROPS_ID;\n    },\n    PAGE_SEGMENT_KEY: function() {\n        return PAGE_SEGMENT_KEY;\n    },\n    GOOGLE_FONT_PROVIDER: function() {\n        return GOOGLE_FONT_PROVIDER;\n    },\n    OPTIMIZED_FONT_PROVIDERS: function() {\n        return OPTIMIZED_FONT_PROVIDERS;\n    },\n    DEFAULT_SERIF_FONT: function() {\n        return DEFAULT_SERIF_FONT;\n    },\n    DEFAULT_SANS_SERIF_FONT: function() {\n        return DEFAULT_SANS_SERIF_FONT;\n    },\n    STATIC_STATUS_PAGES: function() {\n        return STATIC_STATUS_PAGES;\n    },\n    TRACE_OUTPUT_VERSION: function() {\n        return TRACE_OUTPUT_VERSION;\n    },\n    TURBO_TRACE_DEFAULT_MEMORY_LIMIT: function() {\n        return TURBO_TRACE_DEFAULT_MEMORY_LIMIT;\n    },\n    RSC_MODULE_TYPES: function() {\n        return RSC_MODULE_TYPES;\n    },\n    EDGE_UNSUPPORTED_NODE_APIS: function() {\n        return EDGE_UNSUPPORTED_NODE_APIS;\n    },\n    SYSTEM_ENTRYPOINTS: function() {\n        return SYSTEM_ENTRYPOINTS;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _modernbrowserslisttarget = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./modern-browserslist-target */ \"./node_modules/next/dist/shared/lib/modern-browserslist-target.js\"));\nconst COMPILER_NAMES = {\n    client: \"client\",\n    server: \"server\",\n    edgeServer: \"edge-server\"\n};\nconst INTERNAL_HEADERS = [\n    \"x-invoke-error\",\n    \"x-invoke-output\",\n    \"x-invoke-path\",\n    \"x-invoke-query\",\n    \"x-invoke-status\",\n    \"x-middleware-invoke\"\n];\nconst COMPILER_INDEXES = {\n    [COMPILER_NAMES.client]: 0,\n    [COMPILER_NAMES.server]: 1,\n    [COMPILER_NAMES.edgeServer]: 2\n};\nconst PHASE_EXPORT = \"phase-export\";\nconst PHASE_PRODUCTION_BUILD = \"phase-production-build\";\nconst PHASE_PRODUCTION_SERVER = \"phase-production-server\";\nconst PHASE_DEVELOPMENT_SERVER = \"phase-development-server\";\nconst PHASE_TEST = \"phase-test\";\nconst PHASE_INFO = \"phase-info\";\nconst PAGES_MANIFEST = \"pages-manifest.json\";\nconst APP_PATHS_MANIFEST = \"app-paths-manifest.json\";\nconst APP_PATH_ROUTES_MANIFEST = \"app-path-routes-manifest.json\";\nconst BUILD_MANIFEST = \"build-manifest.json\";\nconst APP_BUILD_MANIFEST = \"app-build-manifest.json\";\nconst FUNCTIONS_CONFIG_MANIFEST = \"functions-config-manifest.json\";\nconst SUBRESOURCE_INTEGRITY_MANIFEST = \"subresource-integrity-manifest\";\nconst NEXT_FONT_MANIFEST = \"next-font-manifest\";\nconst EXPORT_MARKER = \"export-marker.json\";\nconst EXPORT_DETAIL = \"export-detail.json\";\nconst PRERENDER_MANIFEST = \"prerender-manifest.json\";\nconst ROUTES_MANIFEST = \"routes-manifest.json\";\nconst IMAGES_MANIFEST = \"images-manifest.json\";\nconst SERVER_FILES_MANIFEST = \"required-server-files.json\";\nconst DEV_CLIENT_PAGES_MANIFEST = \"_devPagesManifest.json\";\nconst MIDDLEWARE_MANIFEST = \"middleware-manifest.json\";\nconst DEV_MIDDLEWARE_MANIFEST = \"_devMiddlewareManifest.json\";\nconst REACT_LOADABLE_MANIFEST = \"react-loadable-manifest.json\";\nconst FONT_MANIFEST = \"font-manifest.json\";\nconst SERVER_DIRECTORY = \"server\";\nconst CONFIG_FILES = [\n    \"next.config.js\",\n    \"next.config.mjs\"\n];\nconst BUILD_ID_FILE = \"BUILD_ID\";\nconst BLOCKED_PAGES = [\n    \"/_document\",\n    \"/_app\",\n    \"/_error\"\n];\nconst CLIENT_PUBLIC_FILES_PATH = \"public\";\nconst CLIENT_STATIC_FILES_PATH = \"static\";\nconst STRING_LITERAL_DROP_BUNDLE = \"__NEXT_DROP_CLIENT_FILE__\";\nconst NEXT_BUILTIN_DOCUMENT = \"__NEXT_BUILTIN_DOCUMENT__\";\nconst BARREL_OPTIMIZATION_PREFIX = \"__barrel_optimize__\";\nconst CLIENT_REFERENCE_MANIFEST = \"client-reference-manifest\";\nconst SERVER_REFERENCE_MANIFEST = \"server-reference-manifest\";\nconst MIDDLEWARE_BUILD_MANIFEST = \"middleware-build-manifest\";\nconst MIDDLEWARE_REACT_LOADABLE_MANIFEST = \"middleware-react-loadable-manifest\";\nconst CLIENT_STATIC_FILES_RUNTIME_MAIN = \"main\";\nconst CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = \"\" + CLIENT_STATIC_FILES_RUNTIME_MAIN + \"-app\";\nconst APP_CLIENT_INTERNALS = \"app-pages-internals\";\nconst CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = \"react-refresh\";\nconst CLIENT_STATIC_FILES_RUNTIME_AMP = \"amp\";\nconst CLIENT_STATIC_FILES_RUNTIME_WEBPACK = \"webpack\";\nconst CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = \"polyfills\";\nconst CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(CLIENT_STATIC_FILES_RUNTIME_POLYFILLS);\nconst EDGE_RUNTIME_WEBPACK = \"edge-runtime-webpack\";\nconst STATIC_PROPS_ID = \"__N_SSG\";\nconst SERVER_PROPS_ID = \"__N_SSP\";\nconst PAGE_SEGMENT_KEY = \"__PAGE__\";\nconst GOOGLE_FONT_PROVIDER = \"https://fonts.googleapis.com/\";\nconst OPTIMIZED_FONT_PROVIDERS = [\n    {\n        url: GOOGLE_FONT_PROVIDER,\n        preconnect: \"https://fonts.gstatic.com\"\n    },\n    {\n        url: \"https://use.typekit.net\",\n        preconnect: \"https://use.typekit.net\"\n    }\n];\nconst DEFAULT_SERIF_FONT = {\n    name: \"Times New Roman\",\n    xAvgCharWidth: 821,\n    azAvgWidth: 854.3953488372093,\n    unitsPerEm: 2048\n};\nconst DEFAULT_SANS_SERIF_FONT = {\n    name: \"Arial\",\n    xAvgCharWidth: 904,\n    azAvgWidth: 934.5116279069767,\n    unitsPerEm: 2048\n};\nconst STATIC_STATUS_PAGES = [\n    \"/500\"\n];\nconst TRACE_OUTPUT_VERSION = 1;\nconst TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000;\nconst RSC_MODULE_TYPES = {\n    client: \"client\",\n    server: \"server\"\n};\nconst EDGE_UNSUPPORTED_NODE_APIS = [\n    \"clearImmediate\",\n    \"setImmediate\",\n    \"BroadcastChannel\",\n    \"ByteLengthQueuingStrategy\",\n    \"CompressionStream\",\n    \"CountQueuingStrategy\",\n    \"DecompressionStream\",\n    \"DomException\",\n    \"MessageChannel\",\n    \"MessageEvent\",\n    \"MessagePort\",\n    \"ReadableByteStreamController\",\n    \"ReadableStreamBYOBRequest\",\n    \"ReadableStreamDefaultController\",\n    \"TransformStreamDefaultController\",\n    \"WritableStreamDefaultController\"\n];\nconst SYSTEM_ENTRYPOINTS = new Set([\n    CLIENT_STATIC_FILES_RUNTIME_MAIN,\n    CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n    CLIENT_STATIC_FILES_RUNTIME_AMP,\n    CLIENT_STATIC_FILES_RUNTIME_MAIN_APP\n]);\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/constants.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    defaultHead: function() {\n        return defaultHead;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"react\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            charSet: \"utf-8\"\n        })\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === \"string\" || typeof child === \"number\") {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    \"name\",\n    \"httpEquiv\",\n    \"charSet\",\n    \"itemProp\"\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case \"title\":\n            case \"base\":\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case \"meta\":\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === \"charSet\") {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n                const srcMessage = c.props[\"src\"] ? '<script> tag with src=\"' + c.props[\"src\"] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props[\"href\"] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ _react.default.createElement(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState)\n    }, children);\n}\nconst _default = Head;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/head.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/is-plain-object.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/is-plain-object.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getObjectClassLabel: function() {\n        return getObjectClassLabel;\n    },\n    isPlainObject: function() {\n        return isPlainObject;\n    }\n});\nfunction getObjectClassLabel(value) {\n    return Object.prototype.toString.call(value);\n}\nfunction isPlainObject(value) {\n    if (getObjectClassLabel(value) !== \"[object Object]\") {\n        return false;\n    }\n    const prototype = Object.getPrototypeOf(value);\n    /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */ return prototype === null || prototype.hasOwnProperty(\"isPrototypeOf\");\n} //# sourceMappingURL=is-plain-object.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/is-plain-object.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/modern-browserslist-target.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/modern-browserslist-target.js ***!
  \*************************************************************************/
/***/ ((module) => {

eval("// Note: This file is JS because it's used by the taskfile-swc.js file, which is JS.\n// Keep file changes in sync with the corresponding `.d.ts` files.\n/**\n * These are the browser versions that support all of the following:\n * static import: https://caniuse.com/es6-module\n * dynamic import: https://caniuse.com/es6-module-dynamic-import\n * import.meta: https://caniuse.com/mdn-javascript_operators_import_meta\n */ \nconst MODERN_BROWSERSLIST_TARGET = [\n    \"chrome 64\",\n    \"edge 79\",\n    \"firefox 67\",\n    \"opera 51\",\n    \"safari 12\"\n];\nmodule.exports = MODERN_BROWSERSLIST_TARGET; //# sourceMappingURL=modern-browserslist-target.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbW9kZXJuLWJyb3dzZXJzbGlzdC10YXJnZXQuanMiLCJtYXBwaW5ncyI6IkFBQUEsb0ZBQW9GO0FBQ3BGLGtFQUFrRTtBQUNsRTs7Ozs7Q0FLQyxHQUFnQjtBQUNqQixNQUFNQSw2QkFBNkI7SUFDL0I7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNIO0FBQ0RDLE9BQU9DLE9BQU8sR0FBR0YsNEJBRWpCLHNEQUFzRCIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbW9kZXJuLWJyb3dzZXJzbGlzdC10YXJnZXQuanM/YmUyOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBOb3RlOiBUaGlzIGZpbGUgaXMgSlMgYmVjYXVzZSBpdCdzIHVzZWQgYnkgdGhlIHRhc2tmaWxlLXN3Yy5qcyBmaWxlLCB3aGljaCBpcyBKUy5cbi8vIEtlZXAgZmlsZSBjaGFuZ2VzIGluIHN5bmMgd2l0aCB0aGUgY29ycmVzcG9uZGluZyBgLmQudHNgIGZpbGVzLlxuLyoqXG4gKiBUaGVzZSBhcmUgdGhlIGJyb3dzZXIgdmVyc2lvbnMgdGhhdCBzdXBwb3J0IGFsbCBvZiB0aGUgZm9sbG93aW5nOlxuICogc3RhdGljIGltcG9ydDogaHR0cHM6Ly9jYW5pdXNlLmNvbS9lczYtbW9kdWxlXG4gKiBkeW5hbWljIGltcG9ydDogaHR0cHM6Ly9jYW5pdXNlLmNvbS9lczYtbW9kdWxlLWR5bmFtaWMtaW1wb3J0XG4gKiBpbXBvcnQubWV0YTogaHR0cHM6Ly9jYW5pdXNlLmNvbS9tZG4tamF2YXNjcmlwdF9vcGVyYXRvcnNfaW1wb3J0X21ldGFcbiAqLyBcInVzZSBzdHJpY3RcIjtcbmNvbnN0IE1PREVSTl9CUk9XU0VSU0xJU1RfVEFSR0VUID0gW1xuICAgIFwiY2hyb21lIDY0XCIsXG4gICAgXCJlZGdlIDc5XCIsXG4gICAgXCJmaXJlZm94IDY3XCIsXG4gICAgXCJvcGVyYSA1MVwiLFxuICAgIFwic2FmYXJpIDEyXCJcbl07XG5tb2R1bGUuZXhwb3J0cyA9IE1PREVSTl9CUk9XU0VSU0xJU1RfVEFSR0VUO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1tb2Rlcm4tYnJvd3NlcnNsaXN0LXRhcmdldC5qcy5tYXAiXSwibmFtZXMiOlsiTU9ERVJOX0JST1dTRVJTTElTVF9UQVJHRVQiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/modern-browserslist-target.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"denormalizePagePath\", ({\n    enumerable: true,\n    get: function() {\n        return denormalizePagePath;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../router/utils */ \"./node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _normalizepathsep = __webpack_require__(/*! ./normalize-path-sep */ \"./node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js\");\nfunction denormalizePagePath(page) {\n    let _page = (0, _normalizepathsep.normalizePathSep)(page);\n    return _page.startsWith(\"/index/\") && !(0, _utils.isDynamicRoute)(_page) ? _page.slice(6) : _page !== \"/index\" ? _page : \"/\";\n} //# sourceMappingURL=denormalize-page-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcGFnZS1wYXRoL2Rlbm9ybWFsaXplLXBhZ2UtcGF0aC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFDekNHLE9BQU87QUFDWCxDQUFDLEVBQUM7QUFDRkgsdURBQXNEO0lBQ2xESSxZQUFZO0lBQ1pDLEtBQUs7UUFDRCxPQUFPQztJQUNYO0FBQ0osQ0FBQyxFQUFDO0FBQ0YsTUFBTUMsU0FBU0MsbUJBQU9BLENBQUMsa0ZBQWlCO0FBQ3hDLE1BQU1DLG9CQUFvQkQsbUJBQU9BLENBQUMsaUdBQXNCO0FBQ3hELFNBQVNGLG9CQUFvQkksSUFBSTtJQUM3QixJQUFJQyxRQUFRLENBQUMsR0FBR0Ysa0JBQWtCRyxnQkFBZ0IsRUFBRUY7SUFDcEQsT0FBT0MsTUFBTUUsVUFBVSxDQUFDLGNBQWMsQ0FBQyxDQUFDLEdBQUdOLE9BQU9PLGNBQWMsRUFBRUgsU0FBU0EsTUFBTUksS0FBSyxDQUFDLEtBQUtKLFVBQVUsV0FBV0EsUUFBUTtBQUM3SCxFQUVBLGlEQUFpRCIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcGFnZS1wYXRoL2Rlbm9ybWFsaXplLXBhZ2UtcGF0aC5qcz9kZTBmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZGVub3JtYWxpemVQYWdlUGF0aFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gZGVub3JtYWxpemVQYWdlUGF0aDtcbiAgICB9XG59KTtcbmNvbnN0IF91dGlscyA9IHJlcXVpcmUoXCIuLi9yb3V0ZXIvdXRpbHNcIik7XG5jb25zdCBfbm9ybWFsaXplcGF0aHNlcCA9IHJlcXVpcmUoXCIuL25vcm1hbGl6ZS1wYXRoLXNlcFwiKTtcbmZ1bmN0aW9uIGRlbm9ybWFsaXplUGFnZVBhdGgocGFnZSkge1xuICAgIGxldCBfcGFnZSA9ICgwLCBfbm9ybWFsaXplcGF0aHNlcC5ub3JtYWxpemVQYXRoU2VwKShwYWdlKTtcbiAgICByZXR1cm4gX3BhZ2Uuc3RhcnRzV2l0aChcIi9pbmRleC9cIikgJiYgISgwLCBfdXRpbHMuaXNEeW5hbWljUm91dGUpKF9wYWdlKSA/IF9wYWdlLnNsaWNlKDYpIDogX3BhZ2UgIT09IFwiL2luZGV4XCIgPyBfcGFnZSA6IFwiL1wiO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZW5vcm1hbGl6ZS1wYWdlLXBhdGguanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsImRlbm9ybWFsaXplUGFnZVBhdGgiLCJfdXRpbHMiLCJyZXF1aXJlIiwiX25vcm1hbGl6ZXBhdGhzZXAiLCJwYWdlIiwiX3BhZ2UiLCJub3JtYWxpemVQYXRoU2VwIiwic3RhcnRzV2l0aCIsImlzRHluYW1pY1JvdXRlIiwic2xpY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ensureLeadingSlash\", ({\n    enumerable: true,\n    get: function() {\n        return ensureLeadingSlash;\n    }\n}));\nfunction ensureLeadingSlash(path) {\n    return path.startsWith(\"/\") ? path : \"/\" + path;\n} //# sourceMappingURL=ensure-leading-slash.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcGFnZS1wYXRoL2Vuc3VyZS1sZWFkaW5nLXNsYXNoLmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQyxHQUFnQjtBQUNqQkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILHNEQUFxRDtJQUNqREksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLFNBQVNBLG1CQUFtQkMsSUFBSTtJQUM1QixPQUFPQSxLQUFLQyxVQUFVLENBQUMsT0FBT0QsT0FBTyxNQUFNQTtBQUMvQyxFQUVBLGdEQUFnRCIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcGFnZS1wYXRoL2Vuc3VyZS1sZWFkaW5nLXNsYXNoLmpzP2NmMzIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBGb3IgYSBnaXZlbiBwYWdlIHBhdGgsIHRoaXMgZnVuY3Rpb24gZW5zdXJlcyB0aGF0IHRoZXJlIGlzIGEgbGVhZGluZyBzbGFzaC5cbiAqIElmIHRoZXJlIGlzIG5vdCBhIGxlYWRpbmcgc2xhc2gsIG9uZSBpcyBhZGRlZCwgb3RoZXJ3aXNlIGl0IGlzIG5vb3AuXG4gKi8gXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJlbnN1cmVMZWFkaW5nU2xhc2hcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGVuc3VyZUxlYWRpbmdTbGFzaDtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGVuc3VyZUxlYWRpbmdTbGFzaChwYXRoKSB7XG4gICAgcmV0dXJuIHBhdGguc3RhcnRzV2l0aChcIi9cIikgPyBwYXRoIDogXCIvXCIgKyBwYXRoO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbnN1cmUtbGVhZGluZy1zbGFzaC5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiZW5zdXJlTGVhZGluZ1NsYXNoIiwicGF0aCIsInN0YXJ0c1dpdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/page-path/normalize-page-path.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/page-path/normalize-page-path.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"normalizePagePath\", ({\n    enumerable: true,\n    get: function() {\n        return normalizePagePath;\n    }\n}));\nconst _ensureleadingslash = __webpack_require__(/*! ./ensure-leading-slash */ \"./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\");\nconst _utils = __webpack_require__(/*! ../router/utils */ \"./node_modules/next/dist/shared/lib/router/utils/index.js\");\nconst _utils1 = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nfunction normalizePagePath(page) {\n    const normalized = /^\\/index(\\/|$)/.test(page) && !(0, _utils.isDynamicRoute)(page) ? \"/index\" + page : page === \"/\" ? \"/index\" : (0, _ensureleadingslash.ensureLeadingSlash)(page);\n    if (true) {\n        const { posix } = __webpack_require__(/*! path */ \"path\");\n        const resolvedPage = posix.normalize(normalized);\n        if (resolvedPage !== normalized) {\n            throw new _utils1.NormalizeError(\"Requested and resolved page mismatch: \" + normalized + \" \" + resolvedPage);\n        }\n    }\n    return normalized;\n} //# sourceMappingURL=normalize-page-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/page-path/normalize-page-path.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"normalizePathSep\", ({\n    enumerable: true,\n    get: function() {\n        return normalizePathSep;\n    }\n}));\nfunction normalizePathSep(path) {\n    return path.replace(/\\\\/g, \"/\");\n} //# sourceMappingURL=normalize-path-sep.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcGFnZS1wYXRoL25vcm1hbGl6ZS1wYXRoLXNlcC5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7OztDQUlDLEdBQWdCO0FBQ2pCQSw4Q0FBNkM7SUFDekNHLE9BQU87QUFDWCxDQUFDLEVBQUM7QUFDRkgsb0RBQW1EO0lBQy9DSSxZQUFZO0lBQ1pDLEtBQUs7UUFDRCxPQUFPQztJQUNYO0FBQ0osQ0FBQyxFQUFDO0FBQ0YsU0FBU0EsaUJBQWlCQyxJQUFJO0lBQzFCLE9BQU9BLEtBQUtDLE9BQU8sQ0FBQyxPQUFPO0FBQy9CLEVBRUEsOENBQThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGFzd29zLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9wYWdlLXBhdGgvbm9ybWFsaXplLXBhdGgtc2VwLmpzP2JhYjgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBGb3IgYSBnaXZlbiBwYWdlIHBhdGgsIHRoaXMgZnVuY3Rpb24gZW5zdXJlcyB0aGF0IHRoZXJlIGlzIG5vIGJhY2tzbGFzaFxuICogZXNjYXBpbmcgc2xhc2hlcyBpbiB0aGUgcGF0aC4gRXhhbXBsZTpcbiAqICAtIGBmb29cXC9iYXJcXC9iYXpgIC0+IGBmb28vYmFyL2JhemBcbiAqLyBcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIm5vcm1hbGl6ZVBhdGhTZXBcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIG5vcm1hbGl6ZVBhdGhTZXA7XG4gICAgfVxufSk7XG5mdW5jdGlvbiBub3JtYWxpemVQYXRoU2VwKHBhdGgpIHtcbiAgICByZXR1cm4gcGF0aC5yZXBsYWNlKC9cXFxcL2csIFwiL1wiKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bm9ybWFsaXplLXBhdGgtc2VwLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJub3JtYWxpemVQYXRoU2VwIiwicGF0aCIsInJlcGxhY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/page-path/normalize-path-sep.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/router/utils/app-paths.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/app-paths.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    normalizeAppPath: function() {\n        return normalizeAppPath;\n    },\n    normalizeRscURL: function() {\n        return normalizeRscURL;\n    }\n});\nconst _ensureleadingslash = __webpack_require__(/*! ../../page-path/ensure-leading-slash */ \"./node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js\");\nconst _segment = __webpack_require__(/*! ../../segment */ \"./node_modules/next/dist/shared/lib/segment.js\");\nfunction normalizeAppPath(route) {\n    return (0, _ensureleadingslash.ensureLeadingSlash)(route.split(\"/\").reduce((pathname, segment, index, segments)=>{\n        // Empty segments are ignored.\n        if (!segment) {\n            return pathname;\n        }\n        // Groups are ignored.\n        if ((0, _segment.isGroupSegment)(segment)) {\n            return pathname;\n        }\n        // Parallel segments are ignored.\n        if (segment[0] === \"@\") {\n            return pathname;\n        }\n        // The last segment (if it's a leaf) should be ignored.\n        if ((segment === \"page\" || segment === \"route\") && index === segments.length - 1) {\n            return pathname;\n        }\n        return pathname + \"/\" + segment;\n    }, \"\"));\n}\nfunction normalizeRscURL(url) {\n    return url.replace(/\\.rsc($|\\?)/, \"$1\");\n} //# sourceMappingURL=app-paths.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/utils/app-paths.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/router/utils/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getSortedRoutes: function() {\n        return _sortedroutes.getSortedRoutes;\n    },\n    isDynamicRoute: function() {\n        return _isdynamic.isDynamicRoute;\n    }\n});\nconst _sortedroutes = __webpack_require__(/*! ./sorted-routes */ \"./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\");\nconst _isdynamic = __webpack_require__(/*! ./is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\"); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUN6Q0csT0FBTztBQUNYLENBQUMsRUFBQztBQUNGLEtBQU1DLENBQUFBLENBR047QUFDQSxTQUFTRyxRQUFRQyxNQUFNLEVBQUVDLEdBQUc7SUFDeEIsSUFBSSxJQUFJQyxRQUFRRCxJQUFJVCxPQUFPQyxjQUFjLENBQUNPLFFBQVFFLE1BQU07UUFDcERDLFlBQVk7UUFDWkMsS0FBS0gsR0FBRyxDQUFDQyxLQUFLO0lBQ2xCO0FBQ0o7QUFDQUgsUUFBUUwsU0FBUztJQUNiRyxpQkFBaUI7UUFDYixPQUFPUSxjQUFjUixlQUFlO0lBQ3hDO0lBQ0FDLGdCQUFnQjtRQUNaLE9BQU9RLFdBQVdSLGNBQWM7SUFDcEM7QUFDSjtBQUNBLE1BQU1PLGdCQUFnQkUsbUJBQU9BLENBQUMsMEZBQWlCO0FBQy9DLE1BQU1ELGFBQWFDLG1CQUFPQSxDQUFDLG9GQUFjLEdBRXpDLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2luZGV4LmpzP2Y1MjUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBnZXRTb3J0ZWRSb3V0ZXM6IG51bGwsXG4gICAgaXNEeW5hbWljUm91dGU6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgZ2V0U29ydGVkUm91dGVzOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9zb3J0ZWRyb3V0ZXMuZ2V0U29ydGVkUm91dGVzO1xuICAgIH0sXG4gICAgaXNEeW5hbWljUm91dGU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2lzZHluYW1pYy5pc0R5bmFtaWNSb3V0ZTtcbiAgICB9XG59KTtcbmNvbnN0IF9zb3J0ZWRyb3V0ZXMgPSByZXF1aXJlKFwiLi9zb3J0ZWQtcm91dGVzXCIpO1xuY29uc3QgX2lzZHluYW1pYyA9IHJlcXVpcmUoXCIuL2lzLWR5bmFtaWNcIik7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsIm1vZHVsZSIsImdldFNvcnRlZFJvdXRlcyIsImlzRHluYW1pY1JvdXRlIiwiX2V4cG9ydCIsInRhcmdldCIsImFsbCIsIm5hbWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiX3NvcnRlZHJvdXRlcyIsIl9pc2R5bmFtaWMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/utils/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isDynamicRoute\", ({\n    enumerable: true,\n    get: function() {\n        return isDynamicRoute;\n    }\n}));\nconst _interceptionroutes = __webpack_require__(/*! ../../../../server/future/helpers/interception-routes */ \"./node_modules/next/dist/server/future/helpers/interception-routes.js\");\n// Identify /[param]/ in route string\nconst TEST_ROUTE = /\\/\\[[^/]+?\\](?=\\/|$)/;\nfunction isDynamicRoute(route) {\n    if ((0, _interceptionroutes.isInterceptionRouteAppPath)(route)) {\n        route = (0, _interceptionroutes.extractInterceptionRouteInformation)(route).interceptedRoute;\n    }\n    return TEST_ROUTE.test(route);\n} //# sourceMappingURL=is-dynamic.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSortedRoutes\", ({\n    enumerable: true,\n    get: function() {\n        return getSortedRoutes;\n    }\n}));\nclass UrlNode {\n    insert(urlPath) {\n        this._insert(urlPath.split(\"/\").filter(Boolean), [], false);\n    }\n    smoosh() {\n        return this._smoosh();\n    }\n    _smoosh(prefix) {\n        if (prefix === void 0) prefix = \"/\";\n        const childrenPaths = [\n            ...this.children.keys()\n        ].sort();\n        if (this.slugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[]\"), 1);\n        }\n        if (this.restSlugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[...]\"), 1);\n        }\n        if (this.optionalRestSlugName !== null) {\n            childrenPaths.splice(childrenPaths.indexOf(\"[[...]]\"), 1);\n        }\n        const routes = childrenPaths.map((c)=>this.children.get(c)._smoosh(\"\" + prefix + c + \"/\")).reduce((prev, curr)=>[\n                ...prev,\n                ...curr\n            ], []);\n        if (this.slugName !== null) {\n            routes.push(...this.children.get(\"[]\")._smoosh(prefix + \"[\" + this.slugName + \"]/\"));\n        }\n        if (!this.placeholder) {\n            const r = prefix === \"/\" ? \"/\" : prefix.slice(0, -1);\n            if (this.optionalRestSlugName != null) {\n                throw new Error('You cannot define a route with the same specificity as a optional catch-all route (\"' + r + '\" and \"' + r + \"[[...\" + this.optionalRestSlugName + ']]\").');\n            }\n            routes.unshift(r);\n        }\n        if (this.restSlugName !== null) {\n            routes.push(...this.children.get(\"[...]\")._smoosh(prefix + \"[...\" + this.restSlugName + \"]/\"));\n        }\n        if (this.optionalRestSlugName !== null) {\n            routes.push(...this.children.get(\"[[...]]\")._smoosh(prefix + \"[[...\" + this.optionalRestSlugName + \"]]/\"));\n        }\n        return routes;\n    }\n    _insert(urlPaths, slugNames, isCatchAll) {\n        if (urlPaths.length === 0) {\n            this.placeholder = false;\n            return;\n        }\n        if (isCatchAll) {\n            throw new Error(\"Catch-all must be the last part of the URL.\");\n        }\n        // The next segment in the urlPaths list\n        let nextSegment = urlPaths[0];\n        // Check if the segment matches `[something]`\n        if (nextSegment.startsWith(\"[\") && nextSegment.endsWith(\"]\")) {\n            // Strip `[` and `]`, leaving only `something`\n            let segmentName = nextSegment.slice(1, -1);\n            let isOptional = false;\n            if (segmentName.startsWith(\"[\") && segmentName.endsWith(\"]\")) {\n                // Strip optional `[` and `]`, leaving only `something`\n                segmentName = segmentName.slice(1, -1);\n                isOptional = true;\n            }\n            if (segmentName.startsWith(\"...\")) {\n                // Strip `...`, leaving only `something`\n                segmentName = segmentName.substring(3);\n                isCatchAll = true;\n            }\n            if (segmentName.startsWith(\"[\") || segmentName.endsWith(\"]\")) {\n                throw new Error(\"Segment names may not start or end with extra brackets ('\" + segmentName + \"').\");\n            }\n            if (segmentName.startsWith(\".\")) {\n                throw new Error(\"Segment names may not start with erroneous periods ('\" + segmentName + \"').\");\n            }\n            function handleSlug(previousSlug, nextSlug) {\n                if (previousSlug !== null) {\n                    // If the specific segment already has a slug but the slug is not `something`\n                    // This prevents collisions like:\n                    // pages/[post]/index.js\n                    // pages/[id]/index.js\n                    // Because currently multiple dynamic params on the same segment level are not supported\n                    if (previousSlug !== nextSlug) {\n                        // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n                        throw new Error(\"You cannot use different slug names for the same dynamic path ('\" + previousSlug + \"' !== '\" + nextSlug + \"').\");\n                    }\n                }\n                slugNames.forEach((slug)=>{\n                    if (slug === nextSlug) {\n                        throw new Error('You cannot have the same slug name \"' + nextSlug + '\" repeat within a single dynamic path');\n                    }\n                    if (slug.replace(/\\W/g, \"\") === nextSegment.replace(/\\W/g, \"\")) {\n                        throw new Error('You cannot have the slug names \"' + slug + '\" and \"' + nextSlug + '\" differ only by non-word symbols within a single dynamic path');\n                    }\n                });\n                slugNames.push(nextSlug);\n            }\n            if (isCatchAll) {\n                if (isOptional) {\n                    if (this.restSlugName != null) {\n                        throw new Error('You cannot use both an required and optional catch-all route at the same level (\"[...' + this.restSlugName + ']\" and \"' + urlPaths[0] + '\" ).');\n                    }\n                    handleSlug(this.optionalRestSlugName, segmentName);\n                    // slugName is kept as it can only be one particular slugName\n                    this.optionalRestSlugName = segmentName;\n                    // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n                    nextSegment = \"[[...]]\";\n                } else {\n                    if (this.optionalRestSlugName != null) {\n                        throw new Error('You cannot use both an optional and required catch-all route at the same level (\"[[...' + this.optionalRestSlugName + ']]\" and \"' + urlPaths[0] + '\").');\n                    }\n                    handleSlug(this.restSlugName, segmentName);\n                    // slugName is kept as it can only be one particular slugName\n                    this.restSlugName = segmentName;\n                    // nextSegment is overwritten to [...] so that it can later be sorted specifically\n                    nextSegment = \"[...]\";\n                }\n            } else {\n                if (isOptional) {\n                    throw new Error('Optional route parameters are not yet supported (\"' + urlPaths[0] + '\").');\n                }\n                handleSlug(this.slugName, segmentName);\n                // slugName is kept as it can only be one particular slugName\n                this.slugName = segmentName;\n                // nextSegment is overwritten to [] so that it can later be sorted specifically\n                nextSegment = \"[]\";\n            }\n        }\n        // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n        if (!this.children.has(nextSegment)) {\n            this.children.set(nextSegment, new UrlNode());\n        }\n        this.children.get(nextSegment)._insert(urlPaths.slice(1), slugNames, isCatchAll);\n    }\n    constructor(){\n        this.placeholder = true;\n        this.children = new Map();\n        this.slugName = null;\n        this.restSlugName = null;\n        this.optionalRestSlugName = null;\n    }\n}\nfunction getSortedRoutes(normalizedPages) {\n    // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n    // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n    // Only 1 dynamic segment per nesting level\n    // So in the case that is test/integration/dynamic-routing it'll be this:\n    // pages/[post]/comments.js\n    // pages/blog/[post]/comment/[id].js\n    // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n    // So in this case `UrlNode` created here has `this.slugName === 'post'`\n    // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n    // Instead what has to be passed through is the upwards path's dynamic names\n    const root = new UrlNode();\n    // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n    normalizedPages.forEach((pagePath)=>root.insert(pagePath));\n    // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n    return root.smoosh();\n} //# sourceMappingURL=sorted-routes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/segment.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/segment.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isGroupSegment\", ({\n    enumerable: true,\n    get: function() {\n        return isGroupSegment;\n    }\n}));\nfunction isGroupSegment(segment) {\n    // Use array[0] for performant purpose\n    return segment[0] === \"(\" && segment.endsWith(\")\");\n} //# sourceMappingURL=segment.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvc2VnbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFDekNHLE9BQU87QUFDWCxDQUFDLEVBQUM7QUFDRkgsa0RBQWlEO0lBQzdDSSxZQUFZO0lBQ1pDLEtBQUs7UUFDRCxPQUFPQztJQUNYO0FBQ0osQ0FBQyxFQUFDO0FBQ0YsU0FBU0EsZUFBZUMsT0FBTztJQUMzQixzQ0FBc0M7SUFDdEMsT0FBT0EsT0FBTyxDQUFDLEVBQUUsS0FBSyxPQUFPQSxRQUFRQyxRQUFRLENBQUM7QUFDbEQsRUFFQSxtQ0FBbUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYXN3b3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL3NlZ21lbnQuanM/ZmY3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImlzR3JvdXBTZWdtZW50XCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBpc0dyb3VwU2VnbWVudDtcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGlzR3JvdXBTZWdtZW50KHNlZ21lbnQpIHtcbiAgICAvLyBVc2UgYXJyYXlbMF0gZm9yIHBlcmZvcm1hbnQgcHVycG9zZVxuICAgIHJldHVybiBzZWdtZW50WzBdID09PSBcIihcIiAmJiBzZWdtZW50LmVuZHNXaXRoKFwiKVwiKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2VnbWVudC5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiaXNHcm91cFNlZ21lbnQiLCJzZWdtZW50IiwiZW5kc1dpdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/segment.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"react\");\nconst isServer = \"undefined\" === \"undefined\";\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect(()=>{\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        return ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n        };\n    });\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect(()=>{\n        if (headManager) {\n            headManager._pendingUpdate = emitChange;\n        }\n        return ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n        };\n    });\n    useClientOnlyEffect(()=>{\n        if (headManager && headManager._pendingUpdate) {\n            headManager._pendingUpdate();\n            headManager._pendingUpdate = null;\n        }\n        return ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n        };\n    });\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/side-effect.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    DecodeError: function() {\n        return DecodeError;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    \"CLS\",\n    \"FCP\",\n    \"FID\",\n    \"INP\",\n    \"LCP\",\n    \"TTFB\"\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : \"\");\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === \"string\" ? Component : Component.displayName || Component.name || \"Unknown\";\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split(\"?\");\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, \"/\").replace(/\\/\\/+/g, \"/\") + (urlParts[1] ? \"?\" + urlParts.slice(1).join(\"?\") : \"\");\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw new Error(message);\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw new Error(message);\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== \"undefined\";\nconst ST = SP && [\n    \"mark\",\n    \"measure\",\n    \"getEntriesByName\"\n].every((method)=>typeof performance[method] === \"function\");\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = \"ENOENT\";\n        this.name = \"PageNotFoundError\";\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = \"ENOENT\";\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/utils.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n    enumerable: true,\n    get: function() {\n        return warnOnce;\n    }\n}));\nlet warnOnce = (_)=>{};\nif (true) {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n} //# sourceMappingURL=warn-once.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvdXRpbHMvd2Fybi1vbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUN6Q0csT0FBTztBQUNYLENBQUMsRUFBQztBQUNGSCw0Q0FBMkM7SUFDdkNJLFlBQVk7SUFDWkMsS0FBSztRQUNELE9BQU9DO0lBQ1g7QUFDSixDQUFDLEVBQUM7QUFDRixJQUFJQSxXQUFXLENBQUNDLEtBQUs7QUFDckIsSUFBSUMsSUFBcUMsRUFBRTtJQUN2QyxNQUFNQyxXQUFXLElBQUlDO0lBQ3JCSixXQUFXLENBQUNLO1FBQ1IsSUFBSSxDQUFDRixTQUFTRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLElBQUksQ0FBQ0g7UUFDakI7UUFDQUYsU0FBU00sR0FBRyxDQUFDSjtJQUNqQjtBQUNKLEVBRUEscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGFzd29zLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanM/NzM5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndhcm5PbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3YXJuT25jZTtcbiAgICB9XG59KTtcbmxldCB3YXJuT25jZSA9IChfKT0+e307XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgY29uc3Qgd2FybmluZ3MgPSBuZXcgU2V0KCk7XG4gICAgd2Fybk9uY2UgPSAobXNnKT0+e1xuICAgICAgICBpZiAoIXdhcm5pbmdzLmhhcyhtc2cpKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4obXNnKTtcbiAgICAgICAgfVxuICAgICAgICB3YXJuaW5ncy5hZGQobXNnKTtcbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD13YXJuLW9uY2UuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsIndhcm5PbmNlIiwiXyIsInByb2Nlc3MiLCJ3YXJuaW5ncyIsIlNldCIsIm1zZyIsImhhcyIsImNvbnNvbGUiLCJ3YXJuIiwiYWRkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/utils/warn-once.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"./node_modules/next/dist/shared/lib/is-plain-object.js\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isplainobject.isPlainObject)(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n}));\nconst UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/helpers/interception-routes.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/helpers/interception-routes.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    INTERCEPTION_ROUTE_MARKERS: function() {\n        return INTERCEPTION_ROUTE_MARKERS;\n    },\n    isInterceptionRouteAppPath: function() {\n        return isInterceptionRouteAppPath;\n    },\n    extractInterceptionRouteInformation: function() {\n        return extractInterceptionRouteInformation;\n    }\n});\nconst _apppaths = __webpack_require__(/*! ../../../shared/lib/router/utils/app-paths */ \"./node_modules/next/dist/shared/lib/router/utils/app-paths.js\");\nconst INTERCEPTION_ROUTE_MARKERS = [\n    \"(..)(..)\",\n    \"(.)\",\n    \"(..)\",\n    \"(...)\"\n];\nfunction isInterceptionRouteAppPath(path) {\n    // TODO-APP: add more serious validation\n    return path.split(\"/\").find((segment)=>INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m))) !== undefined;\n}\nfunction extractInterceptionRouteInformation(path) {\n    let interceptingRoute, marker, interceptedRoute;\n    for (const segment of path.split(\"/\")){\n        marker = INTERCEPTION_ROUTE_MARKERS.find((m)=>segment.startsWith(m));\n        if (marker) {\n            [interceptingRoute, interceptedRoute] = path.split(marker, 2);\n            break;\n        }\n    }\n    if (!interceptingRoute || !marker || !interceptedRoute) {\n        throw new Error(`Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);\n    }\n    interceptingRoute = (0, _apppaths.normalizeAppPath)(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n    ;\n    switch(marker){\n        case \"(.)\":\n            // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n            if (interceptingRoute === \"/\") {\n                interceptedRoute = `/${interceptedRoute}`;\n            } else {\n                interceptedRoute = interceptingRoute + \"/\" + interceptedRoute;\n            }\n            break;\n        case \"(..)\":\n            // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n            if (interceptingRoute === \"/\") {\n                throw new Error(`Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`);\n            }\n            interceptedRoute = interceptingRoute.split(\"/\").slice(0, -1).concat(interceptedRoute).join(\"/\");\n            break;\n        case \"(...)\":\n            // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n            interceptedRoute = \"/\" + interceptedRoute;\n            break;\n        case \"(..)(..)\":\n            // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n            const splitInterceptingRoute = interceptingRoute.split(\"/\");\n            if (splitInterceptingRoute.length <= 2) {\n                throw new Error(`Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`);\n            }\n            interceptedRoute = splitInterceptingRoute.slice(0, -2).concat(interceptedRoute).join(\"/\");\n            break;\n        default:\n            throw new Error(\"Invariant: unexpected marker\");\n    }\n    return {\n        interceptingRoute,\n        interceptedRoute\n    };\n}\n\n//# sourceMappingURL=interception-routes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/helpers/interception-routes.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-kind.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-kind.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouteKind\", ({\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n}));\nvar RouteKind;\n(function(RouteKind) {\n    RouteKind[/**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ \"PAGES\"] = \"PAGES\";\n    RouteKind[/**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ \"PAGES_API\"] = \"PAGES_API\";\n    RouteKind[/**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ \"APP_PAGE\"] = \"APP_PAGE\";\n    RouteKind[/**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ \"APP_ROUTE\"] = \"APP_ROUTE\";\n})(RouteKind || (RouteKind = {}));\n\n//# sourceMappingURL=route-kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDZDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLElBQUksRUFBRSxHQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixJQUFJLEVBQUUsR0FBRztBQUNsQztBQUNBLENBQUMsOEJBQThCOztBQUUvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcz8zOTc5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiUm91dGVLaW5kXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBSb3V0ZUtpbmQ7XG4gICAgfVxufSk7XG52YXIgUm91dGVLaW5kO1xuKGZ1bmN0aW9uKFJvdXRlS2luZCkge1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYFBBR0VTYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYHBhZ2VzL2AuXG4gICAqLyBcIlBBR0VTXCJdID0gXCJQQUdFU1wiO1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYFBBR0VTX0FQSWAgcmVwcmVzZW50cyBhbGwgdGhlIEFQSSByb3V0ZXMgdW5kZXIgYHBhZ2VzL2FwaS9gLlxuICAgKi8gXCJQQUdFU19BUElcIl0gPSBcIlBBR0VTX0FQSVwiO1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYEFQUF9QQUdFYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGBwYWdlLntqLHR9c3sseH1gLlxuICAgKi8gXCJBUFBfUEFHRVwiXSA9IFwiQVBQX1BBR0VcIjtcbiAgICBSb3V0ZUtpbmRbLyoqXG4gICAqIGBBUFBfUk9VVEVgIHJlcHJlc2VudHMgYWxsIHRoZSBBUEkgcm91dGVzIGFuZCBtZXRhZGF0YSByb3V0ZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGByb3V0ZS57aix0fXN7LHh9YC5cbiAgICovIFwiQVBQX1JPVVRFXCJdID0gXCJBUFBfUk9VVEVcIjtcbn0pKFJvdXRlS2luZCB8fCAoUm91dGVLaW5kID0ge30pKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cm91dGUta2luZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-kind.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    if (true) {\n        module.exports = __webpack_require__(/*! next/dist/compiled/next-server/pages.runtime.dev.js */ \"next/dist/compiled/next-server/pages.runtime.dev.js\");\n    } else {}\n}\n\n//# sourceMappingURL=module.compiled.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy9tb2R1bGUuY29tcGlsZWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixJQUFJLEtBQW1DLEVBQUUsRUFFeEMsQ0FBQztBQUNGLFFBQVEsSUFBc0M7QUFDOUMsUUFBUSxzSkFBK0U7QUFDdkYsTUFBTSxLQUFLLEVBSU47QUFDTDs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy9tb2R1bGUuY29tcGlsZWQuanM/YmE0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbmlmIChwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgPT09IFwiZWRnZVwiKSB7XG4gICAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy9tb2R1bGUuanNcIik7XG59IGVsc2Uge1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9uZXh0LXNlcnZlci9wYWdlcy5ydW50aW1lLmRldi5qc1wiKTtcbiAgICB9IGVsc2UgaWYgKHByb2Nlc3MuZW52LlRVUkJPUEFDSykge1xuICAgICAgICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvbmV4dC1zZXJ2ZXIvcGFnZXMtdHVyYm8ucnVudGltZS5wcm9kLmpzXCIpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9uZXh0LXNlcnZlci9wYWdlcy5ydW50aW1lLnByb2QuanNcIik7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1tb2R1bGUuY29tcGlsZWQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js ***!
  \***************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\").vendored.contexts.AmpContext;\n\n//# sourceMappingURL=amp-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy92ZW5kb3JlZC9jb250ZXh0cy9hbXAtY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLCtLQUFpRjs7QUFFakYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYXN3b3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvdmVuZG9yZWQvY29udGV4dHMvYW1wLWNvbnRleHQuanM/YTA1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIi4uLy4uL21vZHVsZS5jb21waWxlZFwiKS52ZW5kb3JlZFtcImNvbnRleHRzXCJdLkFtcENvbnRleHQ7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFtcC1jb250ZXh0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/amp-context.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js ***!
  \************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\").vendored.contexts.HeadManagerContext;\n\n//# sourceMappingURL=head-manager-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy92ZW5kb3JlZC9jb250ZXh0cy9oZWFkLW1hbmFnZXItY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLHVMQUF5Rjs7QUFFekYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYXN3b3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvdmVuZG9yZWQvY29udGV4dHMvaGVhZC1tYW5hZ2VyLWNvbnRleHQuanM/YWRjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIi4uLy4uL21vZHVsZS5jb21waWxlZFwiKS52ZW5kb3JlZFtcImNvbnRleHRzXCJdLkhlYWRNYW5hZ2VyQ29udGV4dDtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVhZC1tYW5hZ2VyLWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/head-manager-context.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js ***!
  \****************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ../../module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\").vendored.contexts.HtmlContext;\n\n//# sourceMappingURL=html-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy92ZW5kb3JlZC9jb250ZXh0cy9odG1sLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixnTEFBa0Y7O0FBRWxGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGFzd29zLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzL3ZlbmRvcmVkL2NvbnRleHRzL2h0bWwtY29udGV4dC5qcz80ZTg1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwiLi4vLi4vbW9kdWxlLmNvbXBpbGVkXCIpLnZlbmRvcmVkW1wiY29udGV4dHNcIl0uSHRtbENvbnRleHQ7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWh0bWwtY29udGV4dC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/html-context.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/get-page-files.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/server/get-page-files.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getPageFiles\", ({\n    enumerable: true,\n    get: function() {\n        return getPageFiles;\n    }\n}));\nconst _denormalizepagepath = __webpack_require__(/*! ../shared/lib/page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizepagepath = __webpack_require__(/*! ../shared/lib/page-path/normalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/normalize-page-path.js\");\nfunction getPageFiles(buildManifest, page) {\n    const normalizedPage = (0, _denormalizepagepath.denormalizePagePath)((0, _normalizepagepath.normalizePagePath)(page));\n    let files = buildManifest.pages[normalizedPage];\n    if (!files) {\n        console.warn(`Could not find files for ${normalizedPage} in .next/build-manifest.json`);\n        return [];\n    }\n    return files;\n}\n\n//# sourceMappingURL=get-page-files.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9nZXQtcGFnZS1maWxlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGdEQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDZCQUE2QixtQkFBTyxDQUFDLDZIQUErQztBQUNwRiwyQkFBMkIsbUJBQU8sQ0FBQyx5SEFBNkM7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsZ0JBQWdCO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGFzd29zLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2dldC1wYWdlLWZpbGVzLmpzP2M4NmIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJnZXRQYWdlRmlsZXNcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGdldFBhZ2VGaWxlcztcbiAgICB9XG59KTtcbmNvbnN0IF9kZW5vcm1hbGl6ZXBhZ2VwYXRoID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvcGFnZS1wYXRoL2Rlbm9ybWFsaXplLXBhZ2UtcGF0aFwiKTtcbmNvbnN0IF9ub3JtYWxpemVwYWdlcGF0aCA9IHJlcXVpcmUoXCIuLi9zaGFyZWQvbGliL3BhZ2UtcGF0aC9ub3JtYWxpemUtcGFnZS1wYXRoXCIpO1xuZnVuY3Rpb24gZ2V0UGFnZUZpbGVzKGJ1aWxkTWFuaWZlc3QsIHBhZ2UpIHtcbiAgICBjb25zdCBub3JtYWxpemVkUGFnZSA9ICgwLCBfZGVub3JtYWxpemVwYWdlcGF0aC5kZW5vcm1hbGl6ZVBhZ2VQYXRoKSgoMCwgX25vcm1hbGl6ZXBhZ2VwYXRoLm5vcm1hbGl6ZVBhZ2VQYXRoKShwYWdlKSk7XG4gICAgbGV0IGZpbGVzID0gYnVpbGRNYW5pZmVzdC5wYWdlc1tub3JtYWxpemVkUGFnZV07XG4gICAgaWYgKCFmaWxlcykge1xuICAgICAgICBjb25zb2xlLndhcm4oYENvdWxkIG5vdCBmaW5kIGZpbGVzIGZvciAke25vcm1hbGl6ZWRQYWdlfSBpbiAubmV4dC9idWlsZC1tYW5pZmVzdC5qc29uYCk7XG4gICAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gICAgcmV0dXJuIGZpbGVzO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXQtcGFnZS1maWxlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/get-page-files.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/htmlescape.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/server/htmlescape.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// This utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ESCAPE_REGEX: function() {\n        return ESCAPE_REGEX;\n    },\n    htmlEscapeJsonString: function() {\n        return htmlEscapeJsonString;\n    }\n});\nconst ESCAPE_LOOKUP = {\n    \"&\": \"\\\\u0026\",\n    \">\": \"\\\\u003e\",\n    \"<\": \"\\\\u003c\",\n    \"\\u2028\": \"\\\\u2028\",\n    \"\\u2029\": \"\\\\u2029\"\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction htmlEscapeJsonString(str) {\n    return str.replace(ESCAPE_REGEX, (match)=>ESCAPE_LOOKUP[match]);\n}\n\n//# sourceMappingURL=htmlescape.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9odG1sZXNjYXBlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLE1BQU0sQ0FHTDtBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGFzd29zLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2h0bWxlc2NhcGUuanM/ZDIxNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIHV0aWxpdHkgaXMgYmFzZWQgb24gaHR0cHM6Ly9naXRodWIuY29tL3plcnRvc2gvaHRtbGVzY2FwZVxuLy8gTGljZW5zZTogaHR0cHM6Ly9naXRodWIuY29tL3plcnRvc2gvaHRtbGVzY2FwZS9ibG9iLzA1MjdjYTcxNTZhNTI0ZDI1NjEwMWJiMzEwYTlmOTcwZjYzMDc4YWQvTElDRU5TRVxuXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBFU0NBUEVfUkVHRVg6IG51bGwsXG4gICAgaHRtbEVzY2FwZUpzb25TdHJpbmc6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgRVNDQVBFX1JFR0VYOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIEVTQ0FQRV9SRUdFWDtcbiAgICB9LFxuICAgIGh0bWxFc2NhcGVKc29uU3RyaW5nOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGh0bWxFc2NhcGVKc29uU3RyaW5nO1xuICAgIH1cbn0pO1xuY29uc3QgRVNDQVBFX0xPT0tVUCA9IHtcbiAgICBcIiZcIjogXCJcXFxcdTAwMjZcIixcbiAgICBcIj5cIjogXCJcXFxcdTAwM2VcIixcbiAgICBcIjxcIjogXCJcXFxcdTAwM2NcIixcbiAgICBcIlxcdTIwMjhcIjogXCJcXFxcdTIwMjhcIixcbiAgICBcIlxcdTIwMjlcIjogXCJcXFxcdTIwMjlcIlxufTtcbmNvbnN0IEVTQ0FQRV9SRUdFWCA9IC9bJj48XFx1MjAyOFxcdTIwMjldL2c7XG5mdW5jdGlvbiBodG1sRXNjYXBlSnNvblN0cmluZyhzdHIpIHtcbiAgICByZXR1cm4gc3RyLnJlcGxhY2UoRVNDQVBFX1JFR0VYLCAobWF0Y2gpPT5FU0NBUEVfTE9PS1VQW21hdGNoXSk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWh0bWxlc2NhcGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/htmlescape.js\n");

/***/ }),

/***/ "./node_modules/next/dist/server/utils.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/server/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isBlockedPage: function() {\n        return isBlockedPage;\n    },\n    cleanAmpPath: function() {\n        return cleanAmpPath;\n    },\n    debounce: function() {\n        return debounce;\n    }\n});\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"./node_modules/next/dist/shared/lib/constants.js\");\nfunction isBlockedPage(page) {\n    return _constants.BLOCKED_PAGES.includes(page);\n}\nfunction cleanAmpPath(pathname) {\n    if (pathname.match(/\\?amp=(y|yes|true|1)/)) {\n        pathname = pathname.replace(/\\?amp=(y|yes|true|1)&?/, \"?\");\n    }\n    if (pathname.match(/&amp=(y|yes|true|1)/)) {\n        pathname = pathname.replace(/&amp=(y|yes|true|1)/, \"\");\n    }\n    pathname = pathname.replace(/\\?$/, \"\");\n    return pathname;\n}\nfunction debounce(fn, ms, maxWait = Infinity) {\n    let timeoutId;\n    // The time the debouncing function was first called during this debounce queue.\n    let startTime = 0;\n    // The time the debouncing function was last called.\n    let lastCall = 0;\n    // The arguments and this context of the last call to the debouncing function.\n    let args, context;\n    // A helper used to that either invokes the debounced function, or\n    // reschedules the timer if a more recent call was made.\n    function run() {\n        const now = Date.now();\n        const diff = lastCall + ms - now;\n        // If the diff is non-positive, then we've waited at least `ms`\n        // milliseconds since the last call. Or if we've waited for longer than the\n        // max wait time, we must call the debounced function.\n        if (diff <= 0 || startTime + maxWait >= now) {\n            // It's important to clear the timeout id before invoking the debounced\n            // function, in case the function calls the debouncing function again.\n            timeoutId = undefined;\n            fn.apply(context, args);\n        } else {\n            // Else, a new call was made after the original timer was scheduled. We\n            // didn't clear the timeout (doing so is very slow), so now we need to\n            // reschedule the timer for the time difference.\n            timeoutId = setTimeout(run, diff);\n        }\n    }\n    return function(...passedArgs) {\n        // The arguments and this context of the most recent call are saved so the\n        // debounced function can be invoked with them later.\n        args = passedArgs;\n        context = this;\n        // Instead of constantly clearing and scheduling a timer, we record the\n        // time of the last call. If a second call comes in before the timer fires,\n        // then we'll reschedule in the run function. Doing this is considerably\n        // faster.\n        lastCall = Date.now();\n        // Only schedule a new timer if we're not currently waiting.\n        if (timeoutId === undefined) {\n            startTime = lastCall;\n            timeoutId = setTimeout(run, ms);\n        }\n    };\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/server/utils.js\n");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/templates/helpers.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/templates/helpers.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hoist\", ({\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n}));\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if (\"then\" in module && typeof module.then === \"function\") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === \"function\" && name === \"default\") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/templates/helpers.js\n");

/***/ }),

/***/ "(api)/./node_modules/next/dist/server/future/route-kind.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-kind.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RouteKind\", ({\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n}));\nvar RouteKind;\n(function(RouteKind) {\n    RouteKind[/**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ \"PAGES\"] = \"PAGES\";\n    RouteKind[/**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ \"PAGES_API\"] = \"PAGES_API\";\n    RouteKind[/**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ \"APP_PAGE\"] = \"APP_PAGE\";\n    RouteKind[/**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ \"APP_ROUTE\"] = \"APP_ROUTE\";\n})(RouteKind || (RouteKind = {}));\n\n//# sourceMappingURL=route-kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDZDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLElBQUksRUFBRSxHQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixJQUFJLEVBQUUsR0FBRztBQUNsQztBQUNBLENBQUMsOEJBQThCOztBQUUvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZC5qcz9mY2ExIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiUm91dGVLaW5kXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBSb3V0ZUtpbmQ7XG4gICAgfVxufSk7XG52YXIgUm91dGVLaW5kO1xuKGZ1bmN0aW9uKFJvdXRlS2luZCkge1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYFBBR0VTYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYHBhZ2VzL2AuXG4gICAqLyBcIlBBR0VTXCJdID0gXCJQQUdFU1wiO1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYFBBR0VTX0FQSWAgcmVwcmVzZW50cyBhbGwgdGhlIEFQSSByb3V0ZXMgdW5kZXIgYHBhZ2VzL2FwaS9gLlxuICAgKi8gXCJQQUdFU19BUElcIl0gPSBcIlBBR0VTX0FQSVwiO1xuICAgIFJvdXRlS2luZFsvKipcbiAgICogYEFQUF9QQUdFYCByZXByZXNlbnRzIGFsbCB0aGUgUmVhY3QgcGFnZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGBwYWdlLntqLHR9c3sseH1gLlxuICAgKi8gXCJBUFBfUEFHRVwiXSA9IFwiQVBQX1BBR0VcIjtcbiAgICBSb3V0ZUtpbmRbLyoqXG4gICAqIGBBUFBfUk9VVEVgIHJlcHJlc2VudHMgYWxsIHRoZSBBUEkgcm91dGVzIGFuZCBtZXRhZGF0YSByb3V0ZXMgdGhhdCBhcmUgdW5kZXIgYGFwcC9gIHdpdGggdGhlXG4gICAqIGZpbGVuYW1lIG9mIGByb3V0ZS57aix0fXN7LHh9YC5cbiAgICovIFwiQVBQX1JPVVRFXCJdID0gXCJBUFBfUk9VVEVcIjtcbn0pKFJvdXRlS2luZCB8fCAoUm91dGVLaW5kID0ge30pKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cm91dGUta2luZC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/server/future/route-kind.js\n");

/***/ }),

/***/ "(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    if (true) {\n        module.exports = __webpack_require__(/*! next/dist/compiled/next-server/pages-api.runtime.dev.js */ \"next/dist/compiled/next-server/pages-api.runtime.dev.js\");\n    } else {}\n}\n\n//# sourceMappingURL=module.compiled.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsSUFBSSxLQUFtQyxFQUFFLEVBRXhDLENBQUM7QUFDRixRQUFRLElBQXNDO0FBQzlDLFFBQVEsOEpBQW1GO0FBQzNGLE1BQU0sS0FBSyxFQUlOO0FBQ0w7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYXN3b3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZC5qcz84YTY5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuaWYgKHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSA9PT0gXCJlZGdlXCIpIHtcbiAgICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuanNcIik7XG59IGVsc2Uge1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiKSB7XG4gICAgICAgIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9uZXh0LXNlcnZlci9wYWdlcy1hcGkucnVudGltZS5kZXYuanNcIik7XG4gICAgfSBlbHNlIGlmIChwcm9jZXNzLmVudi5UVVJCT1BBQ0spIHtcbiAgICAgICAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL25leHQtc2VydmVyL3BhZ2VzLWFwaS10dXJiby5ydW50aW1lLnByb2QuanNcIik7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL25leHQtc2VydmVyL3BhZ2VzLWFwaS5ydW50aW1lLnByb2QuanNcIik7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1tb2R1bGUuY29tcGlsZWQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\n");

/***/ })

};
;