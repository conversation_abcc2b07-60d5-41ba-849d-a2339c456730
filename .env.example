# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Database Configuration (if using direct connection)
DATABASE_URL=postgresql://postgres.nldvdkdredobzgmcjajp:[YOUR-PASSWORD]@aws-0-eu-west-2.pooler.supabase.com:6543/postgres

# App Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Development
NODE_ENV=development
