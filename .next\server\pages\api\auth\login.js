"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/login";
exports.ids = ["pages/api/auth/login"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\login.ts */ \"(api)/./src/pages/api/auth/login.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/login\",\n        pathname: \"/api/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: () => (/* binding */ addToCart),\n/* harmony export */   getCartItems: () => (/* binding */ getCartItems),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   removeFromCart: () => (/* binding */ removeFromCart),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signInWithExistingAPI: () => (/* binding */ signInWithExistingAPI),\n/* harmony export */   signInWithUsername: () => (/* binding */ signInWithUsername),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   updateCartItemQuantity: () => (/* binding */ updateCartItemQuantity)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nldvdkdredobzgmcjajp.supabase.co\" || 0;\nconst supabaseAnonKey = \"your_supabase_anon_key_here\" || 0;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Auth helpers - Custom authentication for username/password\nconst getCurrentUser = async ()=>{\n    // First try to get from Supabase auth\n    const { data: { user } } = await supabase.auth.getUser();\n    if (user) return user;\n    // If no Supabase auth user, check for custom session\n    if (false) {}\n    return null;\n};\n// Custom sign in with username and password (for existing users)\nconst signInWithUsername = async (usernameOrEmail, password)=>{\n    try {\n        // Query your users table directly - check both username and email\n        const { data: users, error } = await supabase.from(\"users\").select(\"*\").or(`username.ilike.${usernameOrEmail},email.ilike.${usernameOrEmail}`);\n        if (error || !users || users.length === 0) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid username or password\"\n                }\n            };\n        }\n        const user = users[0];\n        // TODO: Add proper password verification here\n        // This is where you'd verify the password against your stored hash\n        // For now, we'll create a simple API call to verify credentials\n        // Call your existing authentication endpoint if you have one\n        // Or implement password verification logic here\n        // For demonstration, we'll assume the login is valid\n        // In production, you MUST verify the password properly\n        // Store user session locally (you might want to use a more secure method)\n        if (false) {}\n        return {\n            data: {\n                user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Alternative: Call your existing authentication API\nconst signInWithExistingAPI = async (usernameOrEmail, password)=>{\n    try {\n        // If you have an existing authentication endpoint, call it here\n        const response = await fetch(\"/api/auth/login\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                username: usernameOrEmail,\n                password: password\n            })\n        });\n        if (!response.ok) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid credentials\"\n                }\n            };\n        }\n        const userData = await response.json();\n        // Store user session\n        if (false) {}\n        return {\n            data: {\n                user: userData.user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Fallback to email/password for new users or Supabase auth\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signUp = async (email, password, metadata)=>{\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: metadata\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    // Clear custom session\n    localStorage.removeItem(\"daswos_user_id\");\n    localStorage.removeItem(\"daswos_user_data\");\n    // Also sign out from Supabase auth if applicable\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\n// Database helpers\nconst getProducts = async (filters)=>{\n    let query = supabase.from(\"products\").select(\"*\").order(\"createdAt\", {\n        ascending: false\n    });\n    if (filters?.sphere) {\n        query = query.eq(\"sphere\", filters.sphere);\n    }\n    if (filters?.category) {\n        query = query.eq(\"category\", filters.category);\n    }\n    if (filters?.search) {\n        query = query.ilike(\"name\", `%${filters.search}%`);\n    }\n    if (filters?.limit) {\n        query = query.limit(filters.limit);\n    }\n    const { data, error } = await query;\n    return {\n        data,\n        error\n    };\n};\nconst getCartItems = async (userId)=>{\n    const { data, error } = await supabase.from(\"cartItems\").select(`\n      *,\n      product:products(*)\n    `).eq(\"userId\", userId).order(\"addedAt\", {\n        ascending: false\n    });\n    return {\n        data,\n        error\n    };\n};\nconst addToCart = async (userId, productId, quantity = 1)=>{\n    // Check if item already exists in cart\n    const { data: existingItem } = await supabase.from(\"cartItems\").select(\"*\").eq(\"userId\", userId).eq(\"productId\", productId).single();\n    if (existingItem) {\n        // Update quantity\n        const { data, error } = await supabase.from(\"cartItems\").update({\n            quantity: existingItem.quantity + quantity\n        }).eq(\"id\", existingItem.id).select().single();\n        return {\n            data,\n            error\n        };\n    } else {\n        // Add new item\n        const { data, error } = await supabase.from(\"cartItems\").insert({\n            userId,\n            productId,\n            quantity,\n            addedAt: new Date().toISOString()\n        }).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\nconst removeFromCart = async (cartItemId)=>{\n    const { error } = await supabase.from(\"cartItems\").delete().eq(\"id\", cartItemId);\n    return {\n        error\n    };\n};\nconst updateCartItemQuantity = async (cartItemId, quantity)=>{\n    if (quantity <= 0) {\n        return removeFromCart(cartItemId);\n    }\n    const { data, error } = await supabase.from(\"cartItems\").update({\n        quantity\n    }).eq(\"id\", cartItemId).select().single();\n    return {\n        data,\n        error\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/login.ts":
/*!*************************************!*\
  !*** ./src/pages/api/auth/login.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(api)/./src/lib/supabase.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst scryptAsync = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(crypto__WEBPACK_IMPORTED_MODULE_1__.scrypt);\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { username, password } = req.body;\n        if (!username || !password) {\n            return res.status(400).json({\n                error: \"Username and password are required\"\n            });\n        }\n        // TEMPORARY: Hardcoded test user for development\n        if (username === \"test\" && password === \"test\") {\n            const testUser = {\n                id: \"test-user-id\",\n                username: \"test\",\n                email: \"<EMAIL>\",\n                fullName: \"Test User\",\n                isSeller: false,\n                isAdmin: false,\n                hasSubscription: true,\n                familyOwnerId: null,\n                parentAccountId: null,\n                isChildAccount: false,\n                identityVerified: true,\n                identityVerificationStatus: \"verified\",\n                trustScore: 85,\n                verificationStatus: \"verified\",\n                tier: \"safesphere\"\n            };\n            return res.status(200).json({\n                success: true,\n                user: testUser,\n                message: \"Login successful (test user)\"\n            });\n        }\n        // Query your users table - check both username and email (case-insensitive)\n        const { data: users, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id, username, email, password, fullName, isSeller, isAdmin, hasSubscription, familyOwnerId, parentAccountId, isChildAccount, identityVerified, identityVerificationStatus\").or(`username.ilike.${username},email.ilike.${username}`);\n        if (error || !users || users.length === 0) {\n            return res.status(401).json({\n                error: \"Invalid credentials\"\n            });\n        }\n        const user = users[0];\n        const isPasswordValid = await verifyPassword(password, user);\n        if (!isPasswordValid) {\n            return res.status(401).json({\n                error: \"Invalid credentials\"\n            });\n        }\n        // Remove sensitive data before sending response\n        const { password: _, password_hash: __, ...safeUser } = user;\n        res.status(200).json({\n            success: true,\n            user: safeUser,\n            message: \"Login successful\"\n        });\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n// Password verification function - matches your existing server/auth.ts implementation\nasync function verifyPassword(inputPassword, user) {\n    try {\n        if (!user.password || !inputPassword) {\n            return false;\n        }\n        // Parse the stored password hash (format: hash.salt)\n        const [storedHash, salt] = user.password.split(\".\");\n        if (!storedHash || !salt) {\n            console.error(\"Invalid password format in database\");\n            return false;\n        }\n        // Hash the input password with the stored salt using scrypt\n        const hashedInput = await scryptAsync(inputPassword, salt, 64);\n        const hashedInputHex = hashedInput.toString(\"hex\");\n        // Compare the hashes\n        return hashedInputHex === storedHash;\n    } catch (error) {\n        console.error(\"Password verification error:\", error);\n        return false;\n    }\n}\n// Helper function to hash passwords (for reference - matches your server/auth.ts)\nasync function hashPassword(password) {\n    try {\n        const salt = (0,crypto__WEBPACK_IMPORTED_MODULE_1__.randomBytes)(16).toString(\"hex\");\n        const hashedPassword = await scryptAsync(password, salt, 64);\n        return `${hashedPassword.toString(\"hex\")}.${salt}`;\n    } catch (error) {\n        console.error(\"Password hashing error:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Alternative: If you want to integrate with your existing authentication system\nasync function callExistingAuthSystem(username, password) {\n    // If you have an existing authentication microservice or API\n    // you can call it here instead of implementing password verification\n    try {\n        // Example: Call your existing auth service\n        // const response = await fetch('https://your-auth-service.com/login', {\n        //   method: 'POST',\n        //   headers: { 'Content-Type': 'application/json' },\n        //   body: JSON.stringify({ username, password })\n        // })\n        //\n        // if (!response.ok) {\n        //   return null\n        // }\n        //\n        // return await response.json()\n        return null // Placeholder\n        ;\n    } catch (error) {\n        console.error(\"External auth system error:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/login.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();