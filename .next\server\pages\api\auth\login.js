"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/login";
exports.ids = ["pages/api/auth/login"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\login.ts */ \"(api)/./src/pages/api/auth/login.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/login\",\n        pathname: \"/api/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: () => (/* binding */ addToCart),\n/* harmony export */   getCartItems: () => (/* binding */ getCartItems),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   removeFromCart: () => (/* binding */ removeFromCart),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signInWithExistingAPI: () => (/* binding */ signInWithExistingAPI),\n/* harmony export */   signInWithUsername: () => (/* binding */ signInWithUsername),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   updateCartItemQuantity: () => (/* binding */ updateCartItemQuantity)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nldvdkdredobzgmcjajp.supabase.co\" || 0;\nconst supabaseAnonKey = \"your_supabase_anon_key_here\" || 0;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Auth helpers - Custom authentication for username/password\nconst getCurrentUser = async ()=>{\n    // First try to get from Supabase auth\n    const { data: { user } } = await supabase.auth.getUser();\n    if (user) return user;\n    // If no Supabase auth user, check for custom session\n    const customUserId = localStorage.getItem(\"daswos_user_id\");\n    if (customUserId) {\n        const { data: customUser } = await supabase.from(\"users\").select(\"*\").eq(\"id\", customUserId).single();\n        return customUser;\n    }\n    return null;\n};\n// Custom sign in with username and password (for existing users)\nconst signInWithUsername = async (usernameOrEmail, password)=>{\n    try {\n        // Query your users table directly - check both username and email\n        const { data: users, error } = await supabase.from(\"users\").select(\"*\").or(`username.ilike.${usernameOrEmail},email.ilike.${usernameOrEmail}`);\n        if (error || !users || users.length === 0) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid username or password\"\n                }\n            };\n        }\n        const user = users[0];\n        // TODO: Add proper password verification here\n        // This is where you'd verify the password against your stored hash\n        // For now, we'll create a simple API call to verify credentials\n        // Call your existing authentication endpoint if you have one\n        // Or implement password verification logic here\n        // For demonstration, we'll assume the login is valid\n        // In production, you MUST verify the password properly\n        // Store user session locally (you might want to use a more secure method)\n        if (false) {}\n        return {\n            data: {\n                user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Alternative: Call your existing authentication API\nconst signInWithExistingAPI = async (usernameOrEmail, password)=>{\n    try {\n        // If you have an existing authentication endpoint, call it here\n        const response = await fetch(\"/api/auth/login\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                username: usernameOrEmail,\n                password: password\n            })\n        });\n        if (!response.ok) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid credentials\"\n                }\n            };\n        }\n        const userData = await response.json();\n        // Store user session\n        if (false) {}\n        return {\n            data: {\n                user: userData.user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Fallback to email/password for new users or Supabase auth\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signUp = async (email, password, metadata)=>{\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: metadata\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    // Clear custom session\n    localStorage.removeItem(\"daswos_user_id\");\n    localStorage.removeItem(\"daswos_user_data\");\n    // Also sign out from Supabase auth if applicable\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\n// Database helpers\nconst getProducts = async (filters)=>{\n    let query = supabase.from(\"products\").select(\"*\").order(\"createdAt\", {\n        ascending: false\n    });\n    if (filters?.sphere) {\n        query = query.eq(\"sphere\", filters.sphere);\n    }\n    if (filters?.category) {\n        query = query.eq(\"category\", filters.category);\n    }\n    if (filters?.search) {\n        query = query.ilike(\"name\", `%${filters.search}%`);\n    }\n    if (filters?.limit) {\n        query = query.limit(filters.limit);\n    }\n    const { data, error } = await query;\n    return {\n        data,\n        error\n    };\n};\nconst getCartItems = async (userId)=>{\n    const { data, error } = await supabase.from(\"cartItems\").select(`\n      *,\n      product:products(*)\n    `).eq(\"userId\", userId).order(\"addedAt\", {\n        ascending: false\n    });\n    return {\n        data,\n        error\n    };\n};\nconst addToCart = async (userId, productId, quantity = 1)=>{\n    // Check if item already exists in cart\n    const { data: existingItem } = await supabase.from(\"cartItems\").select(\"*\").eq(\"userId\", userId).eq(\"productId\", productId).single();\n    if (existingItem) {\n        // Update quantity\n        const { data, error } = await supabase.from(\"cartItems\").update({\n            quantity: existingItem.quantity + quantity\n        }).eq(\"id\", existingItem.id).select().single();\n        return {\n            data,\n            error\n        };\n    } else {\n        // Add new item\n        const { data, error } = await supabase.from(\"cartItems\").insert({\n            userId,\n            productId,\n            quantity,\n            addedAt: new Date().toISOString()\n        }).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\nconst removeFromCart = async (cartItemId)=>{\n    const { error } = await supabase.from(\"cartItems\").delete().eq(\"id\", cartItemId);\n    return {\n        error\n    };\n};\nconst updateCartItemQuantity = async (cartItemId, quantity)=>{\n    if (quantity <= 0) {\n        return removeFromCart(cartItemId);\n    }\n    const { data, error } = await supabase.from(\"cartItems\").update({\n        quantity\n    }).eq(\"id\", cartItemId).select().single();\n    return {\n        data,\n        error\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/login.ts":
/*!*************************************!*\
  !*** ./src/pages/api/auth/login.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(api)/./src/lib/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { username, password } = req.body;\n        if (!username || !password) {\n            return res.status(400).json({\n                error: \"Username and password are required\"\n            });\n        }\n        // Query your users table - check both username and email (case-insensitive)\n        const { data: users, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").or(`username.ilike.${username},email.ilike.${username}`);\n        if (error || !users || users.length === 0) {\n            return res.status(401).json({\n                error: \"Invalid credentials\"\n            });\n        }\n        const user = users[0];\n        // TODO: Implement proper password verification\n        // This is where you need to verify the password against your stored hash\n        // \n        // Common approaches:\n        // 1. If you store plain text passwords (NOT RECOMMENDED):\n        //    if (user.password !== password) { return error }\n        //\n        // 2. If you use bcrypt:\n        //    const bcrypt = require('bcrypt')\n        //    const isValid = await bcrypt.compare(password, user.password_hash)\n        //    if (!isValid) { return error }\n        //\n        // 3. If you use another hashing method, implement accordingly\n        //\n        // For now, we'll create a placeholder that you can customize:\n        const isPasswordValid = await verifyPassword(password, user);\n        if (!isPasswordValid) {\n            return res.status(401).json({\n                error: \"Invalid credentials\"\n            });\n        }\n        // Remove sensitive data before sending response\n        const { password: _, password_hash: __, ...safeUser } = user;\n        res.status(200).json({\n            success: true,\n            user: safeUser,\n            message: \"Login successful\"\n        });\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n// Password verification function - CUSTOMIZE THIS FOR YOUR SYSTEM\nasync function verifyPassword(inputPassword, user) {\n    // IMPORTANT: Replace this with your actual password verification logic\n    // Option 1: If you have a password field in your users table\n    // return user.password === inputPassword // NOT SECURE - only for testing\n    // Option 2: If you have a password_hash field and use bcrypt\n    // const bcrypt = require('bcrypt')\n    // return await bcrypt.compare(inputPassword, user.password_hash)\n    // Option 3: If you have a different authentication system\n    // Call your existing authentication service/function here\n    // Option 4: If passwords are stored in a separate table\n    // const { data: authData } = await supabase\n    //   .from('user_auth')\n    //   .select('password_hash')\n    //   .eq('user_id', user.id)\n    //   .single()\n    // return await bcrypt.compare(inputPassword, authData.password_hash)\n    // TEMPORARY: For testing purposes, we'll return true\n    // REMOVE THIS IN PRODUCTION and implement proper password verification\n    console.warn(\"WARNING: Using placeholder password verification. Implement proper verification!\");\n    // You can temporarily check if the user exists to test the flow\n    return true // CHANGE THIS TO ACTUAL PASSWORD VERIFICATION\n    ;\n}\n// Alternative: If you want to integrate with your existing authentication system\nasync function callExistingAuthSystem(username, password) {\n    // If you have an existing authentication microservice or API\n    // you can call it here instead of implementing password verification\n    try {\n        // Example: Call your existing auth service\n        // const response = await fetch('https://your-auth-service.com/login', {\n        //   method: 'POST',\n        //   headers: { 'Content-Type': 'application/json' },\n        //   body: JSON.stringify({ username, password })\n        // })\n        // \n        // if (!response.ok) {\n        //   return null\n        // }\n        // \n        // return await response.json()\n        return null // Placeholder\n        ;\n    } catch (error) {\n        console.error(\"External auth system error:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/login.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();