import { NextApiRequest, NextApiResponse } from 'next'
import { generateSpeech } from '@/lib/openai'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { text, voice = 'nova', speed = 1 } = req.body

    if (!text) {
      return res.status(400).json({ error: 'Text is required' })
    }

    if (text.length > 4000) {
      return res.status(400).json({ error: 'Text too long (max 4000 characters)' })
    }

    // Generate speech using OpenAI TTS
    const audioResponse = await generateSpeech(text, voice)
    
    // Convert response to buffer
    const audioBuffer = Buffer.from(await audioResponse.arrayBuffer())

    // Set appropriate headers
    res.setHeader('Content-Type', 'audio/mpeg')
    res.setHeader('Content-Length', audioBuffer.length)
    res.setHeader('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

    res.status(200).send(audioBuffer)
  } catch (error) {
    console.error('Text-to-speech error:', error)
    res.status(500).json({ 
      error: 'Failed to generate speech',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
