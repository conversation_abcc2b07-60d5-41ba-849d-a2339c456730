# DasWos AI - Fully AI-Controlled Shopping Assistant

A Next.js application featuring an AI-powered shopping assistant that integrates with your existing Supabase database. Users can interact with the <PERSON><PERSON><PERSON> robot through voice commands and text chat to discover products, get recommendations, and manage their shopping cart.

## Features

- 🤖 **AI Robot Interface** - Interactive Daswos robot with animations
- 🎤 **Voice Interaction** - Speech-to-text and text-to-speech using OpenAI
- 🛒 **Smart Shopping Cart** - AI can add/remove items based on conversation
- 🔐 **User Authentication** - Integration with existing Supabase user database
- 📱 **Responsive Design** - Works on desktop and mobile devices
- 🛡️ **Trust Scores** - Display product trust levels and sphere information
- 💬 **Chat History** - Persistent conversation storage

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **AI**: OpenAI GPT-4 for chat, Whisper for speech-to-text, TTS for speech synthesis
- **UI Components**: Radix <PERSON>, shadcn/ui
- **State Management**: React Query

## Prerequisites

Before running this application, you need:

1. **Supabase Project** with your existing DasWos database
2. **OpenAI API Key** for AI functionality
3. **Node.js 18+** installed on your system

## Database Schema

The app expects these tables in your Supabase database:

- `users` - User accounts and profiles
- `products` - Product listings with trust scores and sphere information
- `cartItems` - Shopping cart items
- `daswosAiChats` - AI chat conversations
- `daswosAiChatMessages` - Individual chat messages
- `orders` - User orders
- `categories` - Product categories

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd daswos-ai-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` and add your credentials:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   
   # OpenAI Configuration
   OPENAI_API_KEY=your_openai_api_key
   
   # Database Configuration (optional - for direct connections)
   DATABASE_URL=your_postgresql_connection_string
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Configuration

### Supabase Setup

1. Create a new Supabase project or use your existing one
2. Ensure your database has the required tables (see Database Schema above)
3. Enable Row Level Security (RLS) on sensitive tables
4. Get your project URL and API keys from the Supabase dashboard

### OpenAI Setup

1. Create an OpenAI account at [platform.openai.com](https://platform.openai.com)
2. Generate an API key
3. Ensure you have credits available for API usage

### Voice Features

The app uses:
- **Whisper API** for speech-to-text transcription
- **TTS API** for text-to-speech synthesis
- **Web Audio API** for microphone access

## Usage

1. **Sign In**: Use your existing DasWos marketplace credentials
2. **Start Chatting**: Type or speak to Daswos about what you're looking for
3. **Voice Commands**: Click the microphone button to use voice input
4. **Shopping**: Daswos can search products, make recommendations, and add items to your cart
5. **Cart Management**: View and manage your cart in the sidebar

## API Endpoints

- `POST /api/chat` - Process chat messages and AI responses
- `POST /api/transcribe` - Convert speech to text
- `POST /api/speak` - Convert text to speech

## Customization

### Robot Animations

The robot supports these animation states:
- `idle` - Default state with subtle movements
- `talking` - Active speaking animation
- `listening` - Attentive listening pose
- `thinking` - Processing/thinking animation
- `dancing` - Celebratory dance moves
- `searching` - Looking around for products

### Voice Settings

Users can customize:
- Voice type (alloy, echo, fable, onyx, nova, shimmer)
- Speech speed
- Auto-speak responses
- Microphone sensitivity

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Troubleshooting

### Common Issues

1. **Microphone not working**
   - Check browser permissions
   - Ensure HTTPS in production
   - Verify Web Audio API support

2. **AI responses failing**
   - Check OpenAI API key and credits
   - Verify network connectivity
   - Check API rate limits

3. **Database connection issues**
   - Verify Supabase credentials
   - Check RLS policies
   - Ensure tables exist

4. **Robot images not loading**
   - Verify images are in `public/images/`
   - Check file permissions
   - Ensure correct file paths

### Debug Mode

Set `NODE_ENV=development` to enable:
- Detailed error messages
- Console logging
- Development tools

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section
- Review the GitHub issues
- Contact the development team

## Roadmap

- [ ] Enhanced product recommendations
- [ ] Multi-language support
- [ ] Advanced voice commands
- [ ] Mobile app version
- [ ] Integration with payment systems
- [ ] Analytics dashboard
