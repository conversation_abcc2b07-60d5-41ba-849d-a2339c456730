"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/speak";
exports.ids = ["pages/api/speak"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeak&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeak.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeak&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeak.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_speak_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\speak.ts */ \"(api)/./src/pages/api/speak.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_speak_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_speak_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_speak_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_speak_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/speak\",\n        pathname: \"/api/speak\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_speak_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnNwZWFrJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNhcGklNUNzcGVhay50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUN3RDtBQUN4RDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsb0RBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLG9EQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCxxQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Rhc3dvcy1haS1hcHAvPzIyODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3NyY1xcXFxwYWdlc1xcXFxhcGlcXFxcc3BlYWsudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9zcGVha1wiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3NwZWFrXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeak&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeak.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatCompletion: () => (/* binding */ createChatCompletion),\n/* harmony export */   generateProductRecommendations: () => (/* binding */ generateProductRecommendations),\n/* harmony export */   generateSpeech: () => (/* binding */ generateSpeech),\n/* harmony export */   transcribeAudio: () => (/* binding */ transcribeAudio)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__]);\nopenai__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// System prompt for Daswos AI\nconst DASWOS_SYSTEM_PROMPT = `You are Daswos, a friendly AI shopping assistant for the DasWos marketplace. You help users find products, make purchases, and provide information about items in our database.\n\nKey characteristics:\n- You are helpful, friendly, and knowledgeable about products\n- You can search for products, add items to cart, and provide recommendations\n- You understand both SafeSphere (verified, trusted) and OpenSphere (general marketplace) products\n- You prioritize user safety and trust scores when making recommendations\n- You can process voice commands and respond conversationally\n- You maintain context about the user's shopping preferences and history\n\nAvailable actions:\n1. Search for products based on user queries\n2. Add products to the user's shopping cart\n3. Provide product recommendations\n4. Answer questions about products, pricing, and features\n5. Help with navigation and account management\n\nAlways be conversational and helpful. If you're adding items to cart or making recommendations, explain why you chose those specific products.`;\nconst createChatCompletion = async (messages, userContext)=>{\n    try {\n        const systemMessage = {\n            role: \"system\",\n            content: DASWOS_SYSTEM_PROMPT + (userContext ? `\\n\\nUser context: ${JSON.stringify(userContext)}` : \"\")\n        };\n        const response = await openai.chat.completions.create({\n            model: \"gpt-4\",\n            messages: [\n                systemMessage,\n                ...messages\n            ],\n            temperature: 0.7,\n            max_tokens: 1000,\n            functions: [\n                {\n                    name: \"search_products\",\n                    description: \"Search for products in the DasWos marketplace\",\n                    parameters: {\n                        type: \"object\",\n                        properties: {\n                            query: {\n                                type: \"string\",\n                                description: \"Search query for products\"\n                            },\n                            category: {\n                                type: \"string\",\n                                description: \"Product category to filter by\"\n                            },\n                            sphere: {\n                                type: \"string\",\n                                enum: [\n                                    \"safesphere\",\n                                    \"opensphere\"\n                                ],\n                                description: \"Marketplace sphere to search in\"\n                            },\n                            maxPrice: {\n                                type: \"number\",\n                                description: \"Maximum price filter\"\n                            },\n                            minTrustScore: {\n                                type: \"number\",\n                                description: \"Minimum trust score filter\"\n                            }\n                        },\n                        required: [\n                            \"query\"\n                        ]\n                    }\n                },\n                {\n                    name: \"add_to_cart\",\n                    description: \"Add a product to the user's shopping cart\",\n                    parameters: {\n                        type: \"object\",\n                        properties: {\n                            productId: {\n                                type: \"string\",\n                                description: \"ID of the product to add\"\n                            },\n                            quantity: {\n                                type: \"number\",\n                                description: \"Quantity to add (default: 1)\"\n                            }\n                        },\n                        required: [\n                            \"productId\"\n                        ]\n                    }\n                },\n                {\n                    name: \"get_product_details\",\n                    description: \"Get detailed information about a specific product\",\n                    parameters: {\n                        type: \"object\",\n                        properties: {\n                            productId: {\n                                type: \"string\",\n                                description: \"ID of the product to get details for\"\n                            }\n                        },\n                        required: [\n                            \"productId\"\n                        ]\n                    }\n                }\n            ],\n            function_call: \"auto\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"OpenAI API error:\", error);\n        throw error;\n    }\n};\nconst transcribeAudio = async (audioBlob)=>{\n    try {\n        const formData = new FormData();\n        formData.append(\"file\", audioBlob, \"audio.webm\");\n        formData.append(\"model\", \"whisper-1\");\n        const response = await openai.audio.transcriptions.create({\n            file: audioBlob,\n            model: \"whisper-1\"\n        });\n        return response.text;\n    } catch (error) {\n        console.error(\"Whisper transcription error:\", error);\n        throw error;\n    }\n};\nconst generateSpeech = async (text, voice = \"nova\")=>{\n    try {\n        const response = await openai.audio.speech.create({\n            model: \"tts-1\",\n            voice: voice,\n            input: text\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Text-to-speech error:\", error);\n        throw error;\n    }\n};\nconst generateProductRecommendations = async (userQuery, availableProducts, userPreferences)=>{\n    try {\n        const prompt = `Based on the user query \"${userQuery}\" and their preferences ${JSON.stringify(userPreferences)}, \n    recommend the most suitable products from this list: ${JSON.stringify(availableProducts.slice(0, 20))}\n    \n    Return a JSON array of recommendations with productId, reason, and confidence (0-1).\n    Focus on relevance, trust score, and user preferences.`;\n        const response = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"You are a product recommendation engine. Return only valid JSON.\"\n                },\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.3,\n            max_tokens: 1000\n        });\n        const content = response.choices[0]?.message?.content;\n        if (content) {\n            try {\n                return JSON.parse(content);\n            } catch (parseError) {\n                console.error(\"Failed to parse recommendations JSON:\", parseError);\n                return [];\n            }\n        }\n        return [];\n    } catch (error) {\n        console.error(\"Product recommendation error:\", error);\n        return [];\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/speak.ts":
/*!********************************!*\
  !*** ./src/pages/api/speak.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/openai */ \"(api)/./src/lib/openai.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_openai__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_openai__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { text, voice = \"nova\", speed = 1 } = req.body;\n        if (!text) {\n            return res.status(400).json({\n                error: \"Text is required\"\n            });\n        }\n        if (text.length > 4000) {\n            return res.status(400).json({\n                error: \"Text too long (max 4000 characters)\"\n            });\n        }\n        // Generate speech using OpenAI TTS\n        const audioResponse = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_0__.generateSpeech)(text, voice);\n        // Convert response to buffer\n        const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());\n        // Set appropriate headers\n        res.setHeader(\"Content-Type\", \"audio/mpeg\");\n        res.setHeader(\"Content-Length\", audioBuffer.length);\n        res.setHeader(\"Cache-Control\", \"public, max-age=3600\") // Cache for 1 hour\n        ;\n        res.status(200).send(audioBuffer);\n    } catch (error) {\n        console.error(\"Text-to-speech error:\", error);\n        res.status(500).json({\n            error: \"Failed to generate speech\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/speak.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeak&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeak.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();