import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export interface ProductRecommendation {
  productId: string
  reason: string
  confidence: number
}

export interface DaswosAIResponse {
  message: string
  recommendations?: ProductRecommendation[]
  actions?: {
    type: 'add_to_cart' | 'search_products' | 'show_product_details'
    data: any
  }[]
}

// System prompt for Daswos AI
const DASWOS_SYSTEM_PROMPT = `You are <PERSON><PERSON><PERSON>, a friendly AI shopping assistant for the DasWos marketplace. You help users find products, make purchases, and provide information about items in our database.

Key characteristics:
- You are helpful, friendly, and knowledgeable about products
- You can search for products, add items to cart, and provide recommendations
- You understand both SafeSphere (verified, trusted) and OpenSphere (general marketplace) products
- You prioritize user safety and trust scores when making recommendations
- You can process voice commands and respond conversationally
- You maintain context about the user's shopping preferences and history

Available actions:
1. Search for products based on user queries
2. Add products to the user's shopping cart
3. Provide product recommendations
4. Answer questions about products, pricing, and features
5. Help with navigation and account management

Always be conversational and helpful. If you're adding items to cart or making recommendations, explain why you chose those specific products.`

export const createChatCompletion = async (
  messages: ChatMessage[],
  userContext?: {
    userId?: string
    preferences?: any
    cartItems?: any[]
    recentSearches?: string[]
  }
) => {
  try {
    const systemMessage: ChatMessage = {
      role: 'system',
      content: DASWOS_SYSTEM_PROMPT + (userContext ? `\n\nUser context: ${JSON.stringify(userContext)}` : '')
    }

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [systemMessage, ...messages],
      temperature: 0.7,
      max_tokens: 1000,
      functions: [
        {
          name: 'search_products',
          description: 'Search for products in the DasWos marketplace',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Search query for products'
              },
              category: {
                type: 'string',
                description: 'Product category to filter by'
              },
              sphere: {
                type: 'string',
                enum: ['safesphere', 'opensphere'],
                description: 'Marketplace sphere to search in'
              },
              maxPrice: {
                type: 'number',
                description: 'Maximum price filter'
              },
              minTrustScore: {
                type: 'number',
                description: 'Minimum trust score filter'
              }
            },
            required: ['query']
          }
        },
        {
          name: 'add_to_cart',
          description: 'Add a product to the user\'s shopping cart',
          parameters: {
            type: 'object',
            properties: {
              productId: {
                type: 'string',
                description: 'ID of the product to add'
              },
              quantity: {
                type: 'number',
                description: 'Quantity to add (default: 1)'
              }
            },
            required: ['productId']
          }
        },
        {
          name: 'get_product_details',
          description: 'Get detailed information about a specific product',
          parameters: {
            type: 'object',
            properties: {
              productId: {
                type: 'string',
                description: 'ID of the product to get details for'
              }
            },
            required: ['productId']
          }
        }
      ],
      function_call: 'auto'
    })

    return response
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw error
  }
}

export const transcribeAudio = async (audioBlob: Blob) => {
  try {
    const formData = new FormData()
    formData.append('file', audioBlob, 'audio.webm')
    formData.append('model', 'whisper-1')

    const response = await openai.audio.transcriptions.create({
      file: audioBlob as any,
      model: 'whisper-1',
    })

    return response.text
  } catch (error) {
    console.error('Whisper transcription error:', error)
    throw error
  }
}

export const generateSpeech = async (text: string, voice: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer' = 'nova') => {
  try {
    const response = await openai.audio.speech.create({
      model: 'tts-1',
      voice: voice,
      input: text,
    })

    return response
  } catch (error) {
    console.error('Text-to-speech error:', error)
    throw error
  }
}

export const generateProductRecommendations = async (
  userQuery: string,
  availableProducts: any[],
  userPreferences?: any
) => {
  try {
    const prompt = `Based on the user query "${userQuery}" and their preferences ${JSON.stringify(userPreferences)}, 
    recommend the most suitable products from this list: ${JSON.stringify(availableProducts.slice(0, 20))}
    
    Return a JSON array of recommendations with productId, reason, and confidence (0-1).
    Focus on relevance, trust score, and user preferences.`

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a product recommendation engine. Return only valid JSON.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1000
    })

    const content = response.choices[0]?.message?.content
    if (content) {
      try {
        return JSON.parse(content)
      } catch (parseError) {
        console.error('Failed to parse recommendations JSON:', parseError)
        return []
      }
    }
    return []
  } catch (error) {
    console.error('Product recommendation error:', error)
    return []
  }
}
