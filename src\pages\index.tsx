'use client'

import { useState, useEffect } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { LoginForm } from '@/components/auth/LoginForm'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { RobotInterface } from '@/components/robot/RobotInterface'
import { VoiceChat } from '@/components/voice/VoiceChat'
import { ShoppingCart } from '@/components/cart/ShoppingCart'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Toaster } from '@/components/ui/toaster'
import { useToast } from '@/hooks/use-toast'
import { getCurrentUser, getCartItems, updateCartItemQuantity, removeFromCart } from '@/lib/supabase'
import { User, ChatMessage, CartItem, AnimationState, VoiceSettings } from '@/types'
import { generateId } from '@/lib/utils'
import { ShoppingBag, User as UserIcon, LogOut, Settings } from 'lucide-react'

const queryClient = new QueryClient()

function DaswosApp() {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')
  const [showAuth, setShowAuth] = useState(false)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [robotState, setRobotState] = useState<AnimationState>('idle')
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
    enabled: true,
    autoSpeak: true,
    voice: 'nova',
    speed: 1,
    volume: 0.8
  })
  const [showCart, setShowCart] = useState(false)
  const [isChatLoading, setIsChatLoading] = useState(false)

  const { toast } = useToast()

  // Initialize app
  useEffect(() => {
    initializeApp()
  }, [])

  const initializeApp = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (currentUser) {
        // Fetch user data from your database
        setUser(currentUser as any)
        await loadCartItems(currentUser.id)

        // Welcome message
        setMessages([{
          id: generateId(),
          role: 'assistant',
          content: `Hello ${(currentUser as any).user_metadata?.full_name || 'there'}! I'm Daswos, your AI shopping assistant. How can I help you find products today?`,
          timestamp: new Date()
        }])
      } else {
        setShowAuth(true)
      }
    } catch (error) {
      console.error('Failed to initialize app:', error)
      setShowAuth(true)
    } finally {
      setIsLoading(false)
    }
  }

  const loadCartItems = async (userId: string) => {
    try {
      const { data, error } = await getCartItems(userId)
      if (!error && data) {
        setCartItems(data)
      }
    } catch (error) {
      console.error('Failed to load cart items:', error)
    }
  }

  const handleAuthSuccess = (authUser: any) => {
    setUser(authUser)
    setShowAuth(false)

    // Welcome message for new users
    setMessages([{
      id: generateId(),
      role: 'assistant',
      content: `Welcome to DasWos AI! I'm your personal shopping assistant. I can help you find products, add them to your cart, and answer questions about our marketplace. What would you like to shop for today?`,
      timestamp: new Date()
    }])

    toast({
      title: 'Welcome!',
      description: 'You can now start chatting with Daswos.',
    })
  }

  const handleSendMessage = async (message: string, isVoice = false) => {
    if (!user) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to chat with Daswos.',
        variant: 'destructive',
      })
      return
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content: message,
      timestamp: new Date(),
      metadata: isVoice ? { audioUrl: undefined } : undefined
    }

    setMessages(prev => [...prev, userMessage])
    setRobotState('thinking')
    setIsChatLoading(true)

    try {
      // Send to chat API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          chatHistory: messages,
          userId: user.id,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const data = await response.json()

      // Add assistant message
      const assistantMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: data.message,
        timestamp: new Date(),
        metadata: {
          recommendations: data.recommendations,
          actions: data.actions
        }
      }

      setMessages(prev => [...prev, assistantMessage])
      setRobotState('talking')

      // Handle actions
      if (data.actions) {
        for (const action of data.actions) {
          if (action.type === 'add_to_cart') {
            await loadCartItems(user.id)
          }
        }
      }

      // Return to idle after talking
      setTimeout(() => {
        setRobotState('idle')
      }, 3000)

    } catch (error) {
      console.error('Chat error:', error)

      const errorMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: "I'm sorry, I'm having trouble right now. Please try again in a moment.",
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
      setRobotState('idle')

      toast({
        title: 'Chat error',
        description: 'Failed to get response from Daswos. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsChatLoading(false)
    }
  }

  const handleUpdateCartQuantity = async (itemId: string, quantity: number) => {
    try {
      const { error } = await updateCartItemQuantity(itemId, quantity)
      if (!error && user) {
        await loadCartItems(user.id)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update cart item.',
        variant: 'destructive',
      })
    }
  }

  const handleRemoveFromCart = async (itemId: string) => {
    try {
      const { error } = await removeFromCart(itemId)
      if (!error && user) {
        await loadCartItems(user.id)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to remove item from cart.',
        variant: 'destructive',
      })
    }
  }

  const handleRobotInteraction = (type: string, data?: any) => {
    if (type === 'robot_click') {
      setRobotState('dancing')
      setTimeout(() => setRobotState('idle'), 2000)
    }
  }

  const handleLogout = async () => {
    setUser(null)
    setMessages([])
    setCartItems([])
    setShowAuth(true)
    toast({
      title: 'Logged out',
      description: 'You have been successfully logged out.',
    })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading DasWos AI...</p>
        </div>
      </div>
    )
  }

  if (showAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        {authMode === 'login' ? (
          <LoginForm
            onSuccess={handleAuthSuccess}
            onSwitchToRegister={() => setAuthMode('register')}
          />
        ) : (
          <RegisterForm
            onSuccess={handleAuthSuccess}
            onSwitchToLogin={() => setAuthMode('login')}
          />
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-primary">DasWos AI</h1>
            <div className="text-sm text-muted-foreground">
              AI Shopping Assistant
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCart(!showCart)}
              className="relative"
            >
              <ShoppingBag className="h-4 w-4 mr-2" />
              Cart
              {cartItems.length > 0 && (
                <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
                </span>
              )}
            </Button>

            <div className="flex items-center space-x-2">
              <UserIcon className="h-4 w-4" />
              <span className="text-sm">{(user as any)?.user_metadata?.full_name || user?.email}</span>
            </div>

            <Button variant="ghost" size="sm" onClick={handleLogout}>
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[calc(100vh-200px)]">
          {/* Voice Chat */}
          <div className="lg:col-span-1">
            <VoiceChat
              messages={messages}
              onSendMessage={handleSendMessage}
              onVoiceSettingsChange={setVoiceSettings}
              isLoading={isChatLoading}
              className="h-full min-h-[500px]"
            />
          </div>

          {/* Robot Interface - Center */}
          <div className="lg:col-span-1 flex items-center justify-center">
            <div className="w-full max-w-md">
              <RobotInterface
                state={robotState}
                onInteraction={handleRobotInteraction}
                scale={0.8}
                className="w-full"
              />
            </div>
          </div>

          {/* Shopping Cart */}
          <div className="lg:col-span-1">
            <ShoppingCart
              items={cartItems}
              onUpdateQuantity={handleUpdateCartQuantity}
              onRemoveItem={handleRemoveFromCart}
              onCheckout={() => {
                toast({
                  title: 'Checkout',
                  description: 'Checkout functionality coming soon!',
                })
              }}
            />
          </div>
        </div>
      </main>

      <Toaster />
    </div>
  )
}

export default function Home() {
  return (
    <QueryClientProvider client={queryClient}>
      <DaswosApp />
    </QueryClientProvider>
  )
}
