"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 0,\n            y: 0\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 5;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.05;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.05;\n                robotState.targetView = \"front\";\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 10) * 0.05;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 10) * 3;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 8) * 0.2;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 8 + Math.PI) * 0.2;\n                robotState.targetView = \"front\";\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 2;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.1;\n                robotState.targetView = \"front\";\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 3;\n                robotState.rotation = Math.sin(timeInSeconds * 0.5) * 0.2;\n                robotState.targetView = \"threeQuarter\";\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.05;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 2) * 8;\n                robotState.rotation = Math.sin(robotState.dancePhase) * 0.2;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase) * 0.4;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase + Math.PI) * 0.4;\n                // Cycle through views\n                const viewCycle = Math.floor(timeInSeconds * 0.5) % 4;\n                const views = [\n                    \"front\",\n                    \"threeQuarter\",\n                    \"side\",\n                    \"threeQuarter\"\n                ];\n                robotState.targetView = views[viewCycle];\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.03;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.3;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 5) * 3;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.5) * 0.2;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.5 + Math.PI) * 0.2;\n                // Cycle through views for searching\n                const searchViewCycle = Math.floor(timeInSeconds * 0.33) % 3;\n                const searchViews = [\n                    \"front\",\n                    \"threeQuarter\",\n                    \"side\"\n                ];\n                robotState.targetView = searchViews[searchViewCycle];\n                break;\n        }\n        // Smooth view transitions\n        if (robotState.currentView !== robotState.targetView) {\n            robotState.currentView = robotState.targetView;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Draw robot image\n        const currentImage = robotImages[robotState.currentView] || robotImages[\"front\"];\n        if (currentImage) {\n            ctx.drawImage(currentImage, -currentImage.width / 2, -currentImage.height / 2);\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                width: 400,\n                height: 400,\n                onClick: handleCanvasClick,\n                className: \"cursor-pointer\",\n                style: {\n                    maxWidth: \"100%\",\n                    height: \"auto\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            !imagesLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"Loading Daswos...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});