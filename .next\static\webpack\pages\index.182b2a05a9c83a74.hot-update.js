"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: function() { return /* binding */ addToCart; },\n/* harmony export */   getCartItems: function() { return /* binding */ getCartItems; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getProducts: function() { return /* binding */ getProducts; },\n/* harmony export */   removeFromCart: function() { return /* binding */ removeFromCart; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signInWithUsername: function() { return /* binding */ signInWithUsername; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   signUp: function() { return /* binding */ signUp; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; },\n/* harmony export */   updateCartItemQuantity: function() { return /* binding */ updateCartItemQuantity; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://nldvdkdredobzgmcjajp.supabase.co\" || 0;\nconst supabaseAnonKey = \"your_supabase_anon_key_here\" || 0;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Auth helpers - Custom authentication for username/password\nconst getCurrentUser = async ()=>{\n    // First try to get from Supabase auth\n    const { data: { user } } = await supabase.auth.getUser();\n    if (user) return user;\n    // If no Supabase auth user, check for custom session\n    const customUserId = localStorage.getItem(\"daswos_user_id\");\n    if (customUserId) {\n        const { data: customUser } = await supabase.from(\"users\").select(\"*\").eq(\"id\", customUserId).single();\n        return customUser;\n    }\n    return null;\n};\n// Custom sign in with username and password (for existing users)\nconst signInWithUsername = async (username, password)=>{\n    try {\n        // Query your users table directly\n        const { data: user, error } = await supabase.from(\"users\").select(\"*\").eq(\"username\", username).single();\n        if (error || !user) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid username or password\"\n                }\n            };\n        }\n        // Note: In a real app, you'd verify the password hash here\n        // For now, we'll assume the password verification happens elsewhere\n        // You might need to add password hashing/verification logic\n        // Store user session locally (you might want to use a more secure method)\n        localStorage.setItem(\"daswos_user_id\", user.id);\n        localStorage.setItem(\"daswos_user_data\", JSON.stringify(user));\n        return {\n            data: {\n                user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Fallback to email/password for new users or Supabase auth\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signUp = async (email, password, metadata)=>{\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: metadata\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    // Clear custom session\n    localStorage.removeItem(\"daswos_user_id\");\n    localStorage.removeItem(\"daswos_user_data\");\n    // Also sign out from Supabase auth if applicable\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\n// Database helpers\nconst getProducts = async (filters)=>{\n    let query = supabase.from(\"products\").select(\"*\").order(\"createdAt\", {\n        ascending: false\n    });\n    if (filters === null || filters === void 0 ? void 0 : filters.sphere) {\n        query = query.eq(\"sphere\", filters.sphere);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.category) {\n        query = query.eq(\"category\", filters.category);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.search) {\n        query = query.ilike(\"name\", \"%\".concat(filters.search, \"%\"));\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.limit) {\n        query = query.limit(filters.limit);\n    }\n    const { data, error } = await query;\n    return {\n        data,\n        error\n    };\n};\nconst getCartItems = async (userId)=>{\n    const { data, error } = await supabase.from(\"cartItems\").select(\"\\n      *,\\n      product:products(*)\\n    \").eq(\"userId\", userId).order(\"addedAt\", {\n        ascending: false\n    });\n    return {\n        data,\n        error\n    };\n};\nconst addToCart = async function(userId, productId) {\n    let quantity = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1;\n    // Check if item already exists in cart\n    const { data: existingItem } = await supabase.from(\"cartItems\").select(\"*\").eq(\"userId\", userId).eq(\"productId\", productId).single();\n    if (existingItem) {\n        // Update quantity\n        const { data, error } = await supabase.from(\"cartItems\").update({\n            quantity: existingItem.quantity + quantity\n        }).eq(\"id\", existingItem.id).select().single();\n        return {\n            data,\n            error\n        };\n    } else {\n        // Add new item\n        const { data, error } = await supabase.from(\"cartItems\").insert({\n            userId,\n            productId,\n            quantity,\n            addedAt: new Date().toISOString()\n        }).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\nconst removeFromCart = async (cartItemId)=>{\n    const { error } = await supabase.from(\"cartItems\").delete().eq(\"id\", cartItemId);\n    return {\n        error\n    };\n};\nconst updateCartItemQuantity = async (cartItemId, quantity)=>{\n    if (quantity <= 0) {\n        return removeFromCart(cartItemId);\n    }\n    const { data, error } = await supabase.from(\"cartItems\").update({\n        quantity\n    }).eq(\"id\", cartItemId).select().single();\n    return {\n        data,\n        error\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/supabase.ts\n"));

/***/ })

});