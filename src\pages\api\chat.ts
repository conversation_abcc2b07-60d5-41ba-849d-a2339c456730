import { NextApiRequest, NextApiResponse } from 'next'
import { createChatCompletion } from '@/lib/openai'
import { supabase, getProducts, addToCart, getCurrentUser } from '@/lib/supabase'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { message, chatHistory, userId } = req.body

    if (!message || !userId) {
      return res.status(400).json({ error: 'Message and userId are required' })
    }

    // Get user context
    const user = await getCurrentUser()
    const userContext = user ? {
      userId: user.id,
      preferences: {}, // Could be fetched from user profile
      recentSearches: [], // Could be fetched from search history
    } : undefined

    // Prepare messages for OpenAI
    const messages = [
      ...chatHistory.map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      {
        role: 'user' as const,
        content: message
      }
    ]

    // Get AI response
    const completion = await createChatCompletion(messages, userContext)
    const aiResponse = completion.choices[0]?.message

    if (!aiResponse) {
      throw new Error('No response from AI')
    }

    let responseData: any = {
      message: aiResponse.content,
      recommendations: [],
      actions: []
    }

    // Handle function calls
    if (aiResponse.function_call) {
      const functionName = aiResponse.function_call.name
      const functionArgs = JSON.parse(aiResponse.function_call.arguments || '{}')

      switch (functionName) {
        case 'search_products':
          const { data: products } = await getProducts({
            search: functionArgs.query,
            category: functionArgs.category,
            sphere: functionArgs.sphere,
            limit: 10
          })

          responseData.recommendations = products?.slice(0, 5).map(product => ({
            productId: product.id,
            product,
            reason: `Found ${product.name} matching your search`,
            confidence: 0.8
          })) || []

          responseData.message = `I found ${products?.length || 0} products matching "${functionArgs.query}". Here are my top recommendations:`
          break

        case 'add_to_cart':
          if (user) {
            const { data: cartItem, error } = await addToCart(
              user.id,
              functionArgs.productId,
              functionArgs.quantity || 1
            )

            if (!error && cartItem) {
              responseData.actions.push({
                type: 'add_to_cart',
                data: { productId: functionArgs.productId, quantity: functionArgs.quantity || 1 },
                label: 'Added to cart'
              })
              responseData.message = `Great! I've added that item to your cart.`
            } else {
              responseData.message = `Sorry, I couldn't add that item to your cart. Please try again.`
            }
          } else {
            responseData.message = `Please sign in to add items to your cart.`
          }
          break

        case 'get_product_details':
          const { data: productDetails } = await supabase
            .from('products')
            .select('*')
            .eq('id', functionArgs.productId)
            .single()

          if (productDetails) {
            responseData.message = `Here are the details for ${productDetails.name}: ${productDetails.description || 'No description available'}. Price: $${(productDetails.price / 100).toFixed(2)}. Trust Score: ${productDetails.trustScore}%.`
            responseData.actions.push({
              type: 'show_product_details',
              data: { product: productDetails },
              label: 'View details'
            })
          } else {
            responseData.message = `Sorry, I couldn't find details for that product.`
          }
          break

        default:
          responseData.message = aiResponse.content || "I'm here to help you shop! What are you looking for?"
      }
    }

    // Save chat message to database if user is logged in
    if (user) {
      try {
        // Create or get existing chat
        let { data: existingChat } = await supabase
          .from('daswosAiChats')
          .select('id')
          .eq('userId', user.id)
          .order('createdAt', { ascending: false })
          .limit(1)
          .single()

        let chatId = existingChat?.id

        if (!chatId) {
          const { data: newChat } = await supabase
            .from('daswosAiChats')
            .insert({
              userId: user.id,
              title: message.slice(0, 50) + (message.length > 50 ? '...' : ''),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            })
            .select('id')
            .single()

          chatId = newChat?.id
        }

        if (chatId) {
          // Save user message
          await supabase
            .from('daswosAiChatMessages')
            .insert({
              chatId,
              role: 'user',
              content: message,
              createdAt: new Date().toISOString()
            })

          // Save assistant message
          await supabase
            .from('daswosAiChatMessages')
            .insert({
              chatId,
              role: 'assistant',
              content: responseData.message,
              metadata: {
                recommendations: responseData.recommendations,
                actions: responseData.actions
              },
              createdAt: new Date().toISOString()
            })
        }
      } catch (dbError) {
        console.error('Failed to save chat to database:', dbError)
        // Continue without failing the request
      }
    }

    res.status(200).json(responseData)
  } catch (error) {
    console.error('Chat API error:', error)
    res.status(500).json({ 
      error: 'Failed to process chat message',
      message: "I'm sorry, I'm having trouble right now. Please try again in a moment."
    })
  }
}
