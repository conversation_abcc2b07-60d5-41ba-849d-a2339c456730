"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 0,\n            y: 0\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0\n    });\n    // Load robot images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const imageUrls = {\n            front: \"/images/robot_front_view.png\",\n            side: \"/images/robot_side_view.png\",\n            threeQuarter: \"/images/robot_three_quarter_view.png\",\n            back: \"/images/robot_back_view.png\",\n            top: \"/images/robot_top_view.png\"\n        };\n        const loadImages = async ()=>{\n            const images = {};\n            const loadPromises = Object.entries(imageUrls).map((param)=>{\n                let [key, url] = param;\n                return new Promise((resolve, reject)=>{\n                    const img = new Image();\n                    img.onload = ()=>{\n                        images[key] = img;\n                        resolve();\n                    };\n                    img.onerror = ()=>{\n                        // Create a fallback colored rectangle if image fails to load\n                        const canvas = document.createElement(\"canvas\");\n                        canvas.width = 200;\n                        canvas.height = 200;\n                        const ctx = canvas.getContext(\"2d\");\n                        if (ctx) {\n                            ctx.fillStyle = \"#4285f4\";\n                            ctx.fillRect(0, 0, 200, 200);\n                            ctx.fillStyle = \"#ffffff\";\n                            ctx.font = \"16px Arial\";\n                            ctx.textAlign = \"center\";\n                            ctx.fillText(\"Daswos\", 100, 100);\n                            ctx.fillText(\"Robot\", 100, 120);\n                        }\n                        const fallbackImg = new Image();\n                        fallbackImg.src = canvas.toDataURL();\n                        images[key] = fallbackImg;\n                        resolve();\n                    };\n                    img.src = url;\n                });\n            });\n            try {\n                await Promise.all(loadPromises);\n                setRobotImages(images);\n                setImagesLoaded(true);\n            } catch (error) {\n                console.error(\"Failed to load robot images:\", error);\n                setImagesLoaded(true) // Still set to true to show fallback\n                ;\n            }\n        };\n        loadImages();\n    }, []);\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!imagesLoaded) return;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, [\n        imagesLoaded,\n        robotImages\n    ]);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 5;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.05;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.05;\n                robotState.targetView = \"front\";\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 10) * 0.05;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 10) * 3;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 8) * 0.2;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 8 + Math.PI) * 0.2;\n                robotState.targetView = \"front\";\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 2;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.1;\n                robotState.targetView = \"front\";\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 3;\n                robotState.rotation = Math.sin(timeInSeconds * 0.5) * 0.2;\n                robotState.targetView = \"threeQuarter\";\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.05;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 2) * 8;\n                robotState.rotation = Math.sin(robotState.dancePhase) * 0.2;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase) * 0.4;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase + Math.PI) * 0.4;\n                // Cycle through views\n                const viewCycle = Math.floor(timeInSeconds * 0.5) % 4;\n                const views = [\n                    \"front\",\n                    \"threeQuarter\",\n                    \"side\",\n                    \"threeQuarter\"\n                ];\n                robotState.targetView = views[viewCycle];\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.03;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.3;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 5) * 3;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.5) * 0.2;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.5 + Math.PI) * 0.2;\n                // Cycle through views for searching\n                const searchViewCycle = Math.floor(timeInSeconds * 0.33) % 3;\n                const searchViews = [\n                    \"front\",\n                    \"threeQuarter\",\n                    \"side\"\n                ];\n                robotState.targetView = searchViews[searchViewCycle];\n                break;\n        }\n        // Smooth view transitions\n        if (robotState.currentView !== robotState.targetView) {\n            robotState.currentView = robotState.targetView;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Draw robot image\n        const currentImage = robotImages[robotState.currentView] || robotImages[\"front\"];\n        if (currentImage) {\n            ctx.drawImage(currentImage, -currentImage.width / 2, -currentImage.height / 2);\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                width: 400,\n                height: 400,\n                onClick: handleCanvasClick,\n                className: \"cursor-pointer\",\n                style: {\n                    maxWidth: \"100%\",\n                    height: \"auto\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            !imagesLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"Loading Daswos...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"mM4cll2KwMD2COtFI6+Jj9q1qps=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});