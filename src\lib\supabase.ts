import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types based on your schema
export interface User {
  id: string
  username: string
  email: string
  fullName: string
  businessName?: string
  contactInfo?: any
  address?: any
  trustScore: number
  verificationStatus: 'unverified' | 'pending' | 'verified'
  tier: 'guest' | 'opensphere' | 'safesphere'
  isSeller: boolean
  isAdmin: boolean
  createdAt: string
  updatedAt: string
}

export interface Product {
  id: string
  name: string
  description?: string
  price: number
  image?: string
  trustScore: number
  sphere: 'opensphere' | 'safesphere'
  category?: string
  tags?: string[]
  sellerId: string
  createdAt: string
  updatedAt: string
}

export interface CartItem {
  id: string
  userId: string
  productId: string
  quantity: number
  addedAt: string
  product?: Product
}

export interface DaswosAiChat {
  id: string
  userId: string
  title?: string
  createdAt: string
  updatedAt: string
}

export interface DaswosAiChatMessage {
  id: string
  chatId: string
  role: 'user' | 'assistant'
  content: string
  metadata?: any
  createdAt: string
}

// Auth helpers - Custom authentication for username/password
export const getCurrentUser = async () => {
  // First try to get from Supabase auth
  const { data: { user } } = await supabase.auth.getUser()
  if (user) return user

  // If no Supabase auth user, check for custom session
  const customUserId = localStorage.getItem('daswos_user_id')
  if (customUserId) {
    const { data: customUser } = await supabase
      .from('users')
      .select('*')
      .eq('id', customUserId)
      .single()
    return customUser
  }

  return null
}

// Custom sign in with username and password (for existing users)
export const signInWithUsername = async (usernameOrEmail: string, password: string) => {
  try {
    // Query your users table directly - check both username and email
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .or(`username.ilike.${usernameOrEmail},email.ilike.${usernameOrEmail}`)

    if (error || !users || users.length === 0) {
      return {
        data: null,
        error: { message: 'Invalid username or password' }
      }
    }

    const user = users[0]

    // TODO: Add proper password verification here
    // This is where you'd verify the password against your stored hash
    // For now, we'll create a simple API call to verify credentials

    // Call your existing authentication endpoint if you have one
    // Or implement password verification logic here

    // For demonstration, we'll assume the login is valid
    // In production, you MUST verify the password properly

    // Store user session locally (you might want to use a more secure method)
    if (typeof window !== 'undefined') {
      localStorage.setItem('daswos_user_id', user.id)
      localStorage.setItem('daswos_user_data', JSON.stringify(user))
    }

    return {
      data: { user },
      error: null
    }
  } catch (error) {
    return {
      data: null,
      error: { message: 'Authentication failed' }
    }
  }
}

// Alternative: Call your existing authentication API
export const signInWithExistingAPI = async (usernameOrEmail: string, password: string) => {
  try {
    // If you have an existing authentication endpoint, call it here
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: usernameOrEmail,
        password: password,
      }),
    })

    if (!response.ok) {
      return {
        data: null,
        error: { message: 'Invalid credentials' }
      }
    }

    const userData = await response.json()

    // Store user session
    if (typeof window !== 'undefined') {
      localStorage.setItem('daswos_user_id', userData.user.id)
      localStorage.setItem('daswos_user_data', JSON.stringify(userData.user))
    }

    return {
      data: { user: userData.user },
      error: null
    }
  } catch (error) {
    return {
      data: null,
      error: { message: 'Authentication failed' }
    }
  }
}

// Fallback to email/password for new users or Supabase auth
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  return { data, error }
}

export const signUp = async (email: string, password: string, metadata?: any) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata
    }
  })
  return { data, error }
}

export const signOut = async () => {
  // Clear custom session
  localStorage.removeItem('daswos_user_id')
  localStorage.removeItem('daswos_user_data')

  // Also sign out from Supabase auth if applicable
  const { error } = await supabase.auth.signOut()
  return { error }
}

// Database helpers
export const getProducts = async (filters?: {
  sphere?: string
  category?: string
  search?: string
  limit?: number
}) => {
  let query = supabase
    .from('products')
    .select('*')
    .order('createdAt', { ascending: false })

  if (filters?.sphere) {
    query = query.eq('sphere', filters.sphere)
  }

  if (filters?.category) {
    query = query.eq('category', filters.category)
  }

  if (filters?.search) {
    query = query.ilike('name', `%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  const { data, error } = await query
  return { data, error }
}

export const getCartItems = async (userId: string) => {
  const { data, error } = await supabase
    .from('cartItems')
    .select(`
      *,
      product:products(*)
    `)
    .eq('userId', userId)
    .order('addedAt', { ascending: false })

  return { data, error }
}

export const addToCart = async (userId: string, productId: string, quantity: number = 1) => {
  // Check if item already exists in cart
  const { data: existingItem } = await supabase
    .from('cartItems')
    .select('*')
    .eq('userId', userId)
    .eq('productId', productId)
    .single()

  if (existingItem) {
    // Update quantity
    const { data, error } = await supabase
      .from('cartItems')
      .update({ quantity: existingItem.quantity + quantity })
      .eq('id', existingItem.id)
      .select()
      .single()
    return { data, error }
  } else {
    // Add new item
    const { data, error } = await supabase
      .from('cartItems')
      .insert({
        userId,
        productId,
        quantity,
        addedAt: new Date().toISOString()
      })
      .select()
      .single()
    return { data, error }
  }
}

export const removeFromCart = async (cartItemId: string) => {
  const { error } = await supabase
    .from('cartItems')
    .delete()
    .eq('id', cartItemId)
  return { error }
}

export const updateCartItemQuantity = async (cartItemId: string, quantity: number) => {
  if (quantity <= 0) {
    return removeFromCart(cartItemId)
  }

  const { data, error } = await supabase
    .from('cartItems')
    .update({ quantity })
    .eq('id', cartItemId)
    .select()
    .single()
  return { data, error }
}
