"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 0,\n            y: 0\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation = Math.sin(robotState.dancePhase * 1.5) * 0.4;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors\n        const headColor = \"#4A90E2\";\n        const bodyColor = \"#F5F5F5\";\n        const eyeColor = \"#2C3E50\";\n        const mouthColor = \"#E74C3C\";\n        const armColor = \"#34495E\";\n        // Draw robot body (main torso)\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#2980B9\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes\n        ctx.fillStyle = eyeColor;\n        const eyeY = -70;\n        const eyeSize = 8 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 4;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (line)\n            ctx.moveTo(-12, -45);\n            ctx.lineTo(12, -45);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 500,\n            height: 500,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer\",\n            style: {\n                maxWidth: \"100%\",\n                height: \"auto\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});