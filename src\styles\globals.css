@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the DasWos AI app */
.robot-interface {
  position: relative;
}

.robot-interface canvas {
  border-radius: 0.5rem;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
}

.dark .robot-interface canvas {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
}

/* Voice chat animations */
.voice-chat {
  transition: all 0.3s ease;
}

.voice-recording {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Shopping cart animations */
.cart-item {
  transition: all 0.2s ease;
}

.cart-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Trust score indicators */
.trust-excellent {
  @apply text-green-600 bg-green-50 border-green-200;
}

.trust-high {
  @apply text-blue-600 bg-blue-50 border-blue-200;
}

.trust-medium {
  @apply text-yellow-600 bg-yellow-50 border-yellow-200;
}

.trust-low {
  @apply text-red-600 bg-red-50 border-red-200;
}

.dark .trust-excellent {
  @apply text-green-400 bg-green-900/20 border-green-800;
}

.dark .trust-high {
  @apply text-blue-400 bg-blue-900/20 border-blue-800;
}

.dark .trust-medium {
  @apply text-yellow-400 bg-yellow-900/20 border-yellow-800;
}

.dark .trust-low {
  @apply text-red-400 bg-red-900/20 border-red-800;
}

/* Sphere indicators */
.sphere-safesphere {
  @apply text-green-600 bg-green-50 border-green-200;
}

.sphere-opensphere {
  @apply text-blue-600 bg-blue-50 border-blue-200;
}

.dark .sphere-safesphere {
  @apply text-green-400 bg-green-900/20 border-green-800;
}

.dark .sphere-opensphere {
  @apply text-blue-400 bg-blue-900/20 border-blue-800;
}

/* Loading animations */
.loading-dots {
  display: inline-flex;
  align-items: center;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .robot-interface canvas {
    max-width: 300px;
    max-height: 300px;
  }
  
  .voice-chat {
    height: 300px !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .robot-interface canvas,
  .cart-item,
  .voice-chat {
    transition: none;
  }
  
  .voice-recording,
  .loading-dots::after {
    animation: none;
  }
}

/* Focus styles for better accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}
