"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 0,\n            y: 0\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!imagesLoaded) return;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, [\n        imagesLoaded,\n        robotImages\n    ]);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 5;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.05;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.05;\n                robotState.targetView = \"front\";\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 10) * 0.05;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 10) * 3;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 8) * 0.2;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 8 + Math.PI) * 0.2;\n                robotState.targetView = \"front\";\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 2;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.1;\n                robotState.targetView = \"front\";\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 3;\n                robotState.rotation = Math.sin(timeInSeconds * 0.5) * 0.2;\n                robotState.targetView = \"threeQuarter\";\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.05;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 2) * 8;\n                robotState.rotation = Math.sin(robotState.dancePhase) * 0.2;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase) * 0.4;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase + Math.PI) * 0.4;\n                // Cycle through views\n                const viewCycle = Math.floor(timeInSeconds * 0.5) % 4;\n                const views = [\n                    \"front\",\n                    \"threeQuarter\",\n                    \"side\",\n                    \"threeQuarter\"\n                ];\n                robotState.targetView = views[viewCycle];\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.03;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.3;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 5) * 3;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.5) * 0.2;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.5 + Math.PI) * 0.2;\n                // Cycle through views for searching\n                const searchViewCycle = Math.floor(timeInSeconds * 0.33) % 3;\n                const searchViews = [\n                    \"front\",\n                    \"threeQuarter\",\n                    \"side\"\n                ];\n                robotState.targetView = searchViews[searchViewCycle];\n                break;\n        }\n        // Smooth view transitions\n        if (robotState.currentView !== robotState.targetView) {\n            robotState.currentView = robotState.targetView;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Draw robot image\n        const currentImage = robotImages[robotState.currentView] || robotImages[\"front\"];\n        if (currentImage) {\n            ctx.drawImage(currentImage, -currentImage.width / 2, -currentImage.height / 2);\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                width: 400,\n                height: 400,\n                onClick: handleCanvasClick,\n                className: \"cursor-pointer\",\n                style: {\n                    maxWidth: \"100%\",\n                    height: \"auto\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            !imagesLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"Loading Daswos...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});