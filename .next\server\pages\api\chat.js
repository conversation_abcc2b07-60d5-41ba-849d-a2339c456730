"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/chat";
exports.ids = ["pages/api/chat"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\chat.ts */ \"(api)/./src/pages/api/chat.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/chat\",\n        pathname: \"/api/chat\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmNoYXQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZzcmMlNUNwYWdlcyU1Q2FwaSU1Q2NoYXQudHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDdUQ7QUFDdkQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLG1EQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxtREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQscUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYXN3b3MtYWktYXBwLz9jOTBiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9zcmNcXFxccGFnZXNcXFxcYXBpXFxcXGNoYXQudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jaGF0XCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY2hhdFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatCompletion: () => (/* binding */ createChatCompletion),\n/* harmony export */   generateProductRecommendations: () => (/* binding */ generateProductRecommendations),\n/* harmony export */   generateSpeech: () => (/* binding */ generateSpeech),\n/* harmony export */   transcribeAudio: () => (/* binding */ transcribeAudio)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__]);\nopenai__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// System prompt for Daswos AI\nconst DASWOS_SYSTEM_PROMPT = `You are Daswos, a friendly AI shopping assistant for the DasWos marketplace. You help users find products, make purchases, and provide information about items in our database.\n\nKey characteristics:\n- You are helpful, friendly, and knowledgeable about products\n- You can search for products, add items to cart, and provide recommendations\n- You understand both SafeSphere (verified, trusted) and OpenSphere (general marketplace) products\n- You prioritize user safety and trust scores when making recommendations\n- You can process voice commands and respond conversationally\n- You maintain context about the user's shopping preferences and history\n\nAvailable actions:\n1. Search for products based on user queries\n2. Add products to the user's shopping cart\n3. Provide product recommendations\n4. Answer questions about products, pricing, and features\n5. Help with navigation and account management\n\nAlways be conversational and helpful. If you're adding items to cart or making recommendations, explain why you chose those specific products.`;\nconst createChatCompletion = async (messages, userContext)=>{\n    try {\n        const systemMessage = {\n            role: \"system\",\n            content: DASWOS_SYSTEM_PROMPT + (userContext ? `\\n\\nUser context: ${JSON.stringify(userContext)}` : \"\")\n        };\n        const response = await openai.chat.completions.create({\n            model: \"gpt-4\",\n            messages: [\n                systemMessage,\n                ...messages\n            ],\n            temperature: 0.7,\n            max_tokens: 1000,\n            functions: [\n                {\n                    name: \"search_products\",\n                    description: \"Search for products in the DasWos marketplace\",\n                    parameters: {\n                        type: \"object\",\n                        properties: {\n                            query: {\n                                type: \"string\",\n                                description: \"Search query for products\"\n                            },\n                            category: {\n                                type: \"string\",\n                                description: \"Product category to filter by\"\n                            },\n                            sphere: {\n                                type: \"string\",\n                                enum: [\n                                    \"safesphere\",\n                                    \"opensphere\"\n                                ],\n                                description: \"Marketplace sphere to search in\"\n                            },\n                            maxPrice: {\n                                type: \"number\",\n                                description: \"Maximum price filter\"\n                            },\n                            minTrustScore: {\n                                type: \"number\",\n                                description: \"Minimum trust score filter\"\n                            }\n                        },\n                        required: [\n                            \"query\"\n                        ]\n                    }\n                },\n                {\n                    name: \"add_to_cart\",\n                    description: \"Add a product to the user's shopping cart\",\n                    parameters: {\n                        type: \"object\",\n                        properties: {\n                            productId: {\n                                type: \"string\",\n                                description: \"ID of the product to add\"\n                            },\n                            quantity: {\n                                type: \"number\",\n                                description: \"Quantity to add (default: 1)\"\n                            }\n                        },\n                        required: [\n                            \"productId\"\n                        ]\n                    }\n                },\n                {\n                    name: \"get_product_details\",\n                    description: \"Get detailed information about a specific product\",\n                    parameters: {\n                        type: \"object\",\n                        properties: {\n                            productId: {\n                                type: \"string\",\n                                description: \"ID of the product to get details for\"\n                            }\n                        },\n                        required: [\n                            \"productId\"\n                        ]\n                    }\n                }\n            ],\n            function_call: \"auto\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"OpenAI API error:\", error);\n        throw error;\n    }\n};\nconst transcribeAudio = async (audioBlob)=>{\n    try {\n        const formData = new FormData();\n        formData.append(\"file\", audioBlob, \"audio.webm\");\n        formData.append(\"model\", \"whisper-1\");\n        const response = await openai.audio.transcriptions.create({\n            file: audioBlob,\n            model: \"whisper-1\"\n        });\n        return response.text;\n    } catch (error) {\n        console.error(\"Whisper transcription error:\", error);\n        throw error;\n    }\n};\nconst generateSpeech = async (text, voice = \"nova\")=>{\n    try {\n        const response = await openai.audio.speech.create({\n            model: \"tts-1\",\n            voice: voice,\n            input: text\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Text-to-speech error:\", error);\n        throw error;\n    }\n};\nconst generateProductRecommendations = async (userQuery, availableProducts, userPreferences)=>{\n    try {\n        const prompt = `Based on the user query \"${userQuery}\" and their preferences ${JSON.stringify(userPreferences)}, \n    recommend the most suitable products from this list: ${JSON.stringify(availableProducts.slice(0, 20))}\n    \n    Return a JSON array of recommendations with productId, reason, and confidence (0-1).\n    Focus on relevance, trust score, and user preferences.`;\n        const response = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"You are a product recommendation engine. Return only valid JSON.\"\n                },\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.3,\n            max_tokens: 1000\n        });\n        const content = response.choices[0]?.message?.content;\n        if (content) {\n            try {\n                return JSON.parse(content);\n            } catch (parseError) {\n                console.error(\"Failed to parse recommendations JSON:\", parseError);\n                return [];\n            }\n        }\n        return [];\n    } catch (error) {\n        console.error(\"Product recommendation error:\", error);\n        return [];\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(api)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToCart: () => (/* binding */ addToCart),\n/* harmony export */   getCartItems: () => (/* binding */ getCartItems),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   removeFromCart: () => (/* binding */ removeFromCart),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signInWithExistingAPI: () => (/* binding */ signInWithExistingAPI),\n/* harmony export */   signInWithUsername: () => (/* binding */ signInWithUsername),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   updateCartItemQuantity: () => (/* binding */ updateCartItemQuantity)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://nldvdkdredobzgmcjajp.supabase.co\" || 0;\nconst supabaseAnonKey = \"your_supabase_anon_key_here\" || 0;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Auth helpers - Custom authentication for username/password\nconst getCurrentUser = async ()=>{\n    // First try to get from Supabase auth\n    const { data: { user } } = await supabase.auth.getUser();\n    if (user) return user;\n    // If no Supabase auth user, check for custom session\n    if (false) {}\n    return null;\n};\n// Custom sign in with username and password (for existing users)\nconst signInWithUsername = async (usernameOrEmail, password)=>{\n    try {\n        // Query your users table directly - check both username and email\n        const { data: users, error } = await supabase.from(\"users\").select(\"*\").or(`username.ilike.${usernameOrEmail},email.ilike.${usernameOrEmail}`);\n        if (error || !users || users.length === 0) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid username or password\"\n                }\n            };\n        }\n        const user = users[0];\n        // TODO: Add proper password verification here\n        // This is where you'd verify the password against your stored hash\n        // For now, we'll create a simple API call to verify credentials\n        // Call your existing authentication endpoint if you have one\n        // Or implement password verification logic here\n        // For demonstration, we'll assume the login is valid\n        // In production, you MUST verify the password properly\n        // Store user session locally (you might want to use a more secure method)\n        if (false) {}\n        return {\n            data: {\n                user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Alternative: Call your existing authentication API\nconst signInWithExistingAPI = async (usernameOrEmail, password)=>{\n    try {\n        // If you have an existing authentication endpoint, call it here\n        const response = await fetch(\"/api/auth/login\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                username: usernameOrEmail,\n                password: password\n            })\n        });\n        if (!response.ok) {\n            return {\n                data: null,\n                error: {\n                    message: \"Invalid credentials\"\n                }\n            };\n        }\n        const userData = await response.json();\n        // Store user session\n        if (false) {}\n        return {\n            data: {\n                user: userData.user\n            },\n            error: null\n        };\n    } catch (error) {\n        return {\n            data: null,\n            error: {\n                message: \"Authentication failed\"\n            }\n        };\n    }\n};\n// Fallback to email/password for new users or Supabase auth\nconst signIn = async (email, password)=>{\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signUp = async (email, password, metadata)=>{\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: metadata\n        }\n    });\n    return {\n        data,\n        error\n    };\n};\nconst signOut = async ()=>{\n    // Clear custom session\n    localStorage.removeItem(\"daswos_user_id\");\n    localStorage.removeItem(\"daswos_user_data\");\n    // Also sign out from Supabase auth if applicable\n    const { error } = await supabase.auth.signOut();\n    return {\n        error\n    };\n};\n// Database helpers\nconst getProducts = async (filters)=>{\n    let query = supabase.from(\"products\").select(\"*\").order(\"createdAt\", {\n        ascending: false\n    });\n    if (filters?.sphere) {\n        query = query.eq(\"sphere\", filters.sphere);\n    }\n    if (filters?.category) {\n        query = query.eq(\"category\", filters.category);\n    }\n    if (filters?.search) {\n        query = query.ilike(\"name\", `%${filters.search}%`);\n    }\n    if (filters?.limit) {\n        query = query.limit(filters.limit);\n    }\n    const { data, error } = await query;\n    return {\n        data,\n        error\n    };\n};\nconst getCartItems = async (userId)=>{\n    const { data, error } = await supabase.from(\"cartItems\").select(`\n      *,\n      product:products(*)\n    `).eq(\"userId\", userId).order(\"addedAt\", {\n        ascending: false\n    });\n    return {\n        data,\n        error\n    };\n};\nconst addToCart = async (userId, productId, quantity = 1)=>{\n    // Check if item already exists in cart\n    const { data: existingItem } = await supabase.from(\"cartItems\").select(\"*\").eq(\"userId\", userId).eq(\"productId\", productId).single();\n    if (existingItem) {\n        // Update quantity\n        const { data, error } = await supabase.from(\"cartItems\").update({\n            quantity: existingItem.quantity + quantity\n        }).eq(\"id\", existingItem.id).select().single();\n        return {\n            data,\n            error\n        };\n    } else {\n        // Add new item\n        const { data, error } = await supabase.from(\"cartItems\").insert({\n            userId,\n            productId,\n            quantity,\n            addedAt: new Date().toISOString()\n        }).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\nconst removeFromCart = async (cartItemId)=>{\n    const { error } = await supabase.from(\"cartItems\").delete().eq(\"id\", cartItemId);\n    return {\n        error\n    };\n};\nconst updateCartItemQuantity = async (cartItemId, quantity)=>{\n    if (quantity <= 0) {\n        return removeFromCart(cartItemId);\n    }\n    const { data, error } = await supabase.from(\"cartItems\").update({\n        quantity\n    }).eq(\"id\", cartItemId).select().single();\n    return {\n        data,\n        error\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/chat.ts":
/*!*******************************!*\
  !*** ./src/pages/api/chat.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/openai */ \"(api)/./src/lib/openai.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(api)/./src/lib/supabase.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_openai__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_openai__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        const { message, chatHistory, userId } = req.body;\n        if (!message || !userId) {\n            return res.status(400).json({\n                error: \"Message and userId are required\"\n            });\n        }\n        // Get user context\n        const user = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.getCurrentUser)();\n        const userContext = user ? {\n            userId: user.id,\n            preferences: {},\n            recentSearches: []\n        } : undefined;\n        // Prepare messages for OpenAI\n        const messages = [\n            ...chatHistory.map((msg)=>({\n                    role: msg.role,\n                    content: msg.content\n                })),\n            {\n                role: \"user\",\n                content: message\n            }\n        ];\n        // Get AI response\n        const completion = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_0__.createChatCompletion)(messages, userContext);\n        const aiResponse = completion.choices[0]?.message;\n        if (!aiResponse) {\n            throw new Error(\"No response from AI\");\n        }\n        let responseData = {\n            message: aiResponse.content,\n            recommendations: [],\n            actions: []\n        };\n        // Handle function calls\n        if (aiResponse.function_call) {\n            const functionName = aiResponse.function_call.name;\n            const functionArgs = JSON.parse(aiResponse.function_call.arguments || \"{}\");\n            switch(functionName){\n                case \"search_products\":\n                    const { data: products } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.getProducts)({\n                        search: functionArgs.query,\n                        category: functionArgs.category,\n                        sphere: functionArgs.sphere,\n                        limit: 10\n                    });\n                    responseData.recommendations = products?.slice(0, 5).map((product)=>({\n                            productId: product.id,\n                            product,\n                            reason: `Found ${product.name} matching your search`,\n                            confidence: 0.8\n                        })) || [];\n                    responseData.message = `I found ${products?.length || 0} products matching \"${functionArgs.query}\". Here are my top recommendations:`;\n                    break;\n                case \"add_to_cart\":\n                    if (user) {\n                        const { data: cartItem, error } = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.addToCart)(user.id, functionArgs.productId, functionArgs.quantity || 1);\n                        if (!error && cartItem) {\n                            responseData.actions.push({\n                                type: \"add_to_cart\",\n                                data: {\n                                    productId: functionArgs.productId,\n                                    quantity: functionArgs.quantity || 1\n                                },\n                                label: \"Added to cart\"\n                            });\n                            responseData.message = `Great! I've added that item to your cart.`;\n                        } else {\n                            responseData.message = `Sorry, I couldn't add that item to your cart. Please try again.`;\n                        }\n                    } else {\n                        responseData.message = `Please sign in to add items to your cart.`;\n                    }\n                    break;\n                case \"get_product_details\":\n                    const { data: productDetails } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"products\").select(\"*\").eq(\"id\", functionArgs.productId).single();\n                    if (productDetails) {\n                        responseData.message = `Here are the details for ${productDetails.name}: ${productDetails.description || \"No description available\"}. Price: $${(productDetails.price / 100).toFixed(2)}. Trust Score: ${productDetails.trustScore}%.`;\n                        responseData.actions.push({\n                            type: \"show_product_details\",\n                            data: {\n                                product: productDetails\n                            },\n                            label: \"View details\"\n                        });\n                    } else {\n                        responseData.message = `Sorry, I couldn't find details for that product.`;\n                    }\n                    break;\n                default:\n                    responseData.message = aiResponse.content || \"I'm here to help you shop! What are you looking for?\";\n            }\n        }\n        // Save chat message to database if user is logged in\n        if (user) {\n            try {\n                // Create or get existing chat\n                let { data: existingChat } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"daswosAiChats\").select(\"id\").eq(\"userId\", user.id).order(\"createdAt\", {\n                    ascending: false\n                }).limit(1).single();\n                let chatId = existingChat?.id;\n                if (!chatId) {\n                    const { data: newChat } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"daswosAiChats\").insert({\n                        userId: user.id,\n                        title: message.slice(0, 50) + (message.length > 50 ? \"...\" : \"\"),\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    }).select(\"id\").single();\n                    chatId = newChat?.id;\n                }\n                if (chatId) {\n                    // Save user message\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"daswosAiChatMessages\").insert({\n                        chatId,\n                        role: \"user\",\n                        content: message,\n                        createdAt: new Date().toISOString()\n                    });\n                    // Save assistant message\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"daswosAiChatMessages\").insert({\n                        chatId,\n                        role: \"assistant\",\n                        content: responseData.message,\n                        metadata: {\n                            recommendations: responseData.recommendations,\n                            actions: responseData.actions\n                        },\n                        createdAt: new Date().toISOString()\n                    });\n                }\n            } catch (dbError) {\n                console.error(\"Failed to save chat to database:\", dbError);\n            // Continue without failing the request\n            }\n        }\n        res.status(200).json(responseData);\n    } catch (error) {\n        console.error(\"Chat API error:\", error);\n        res.status(500).json({\n            error: \"Failed to process chat message\",\n            message: \"I'm sorry, I'm having trouble right now. Please try again in a moment.\"\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/chat.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();