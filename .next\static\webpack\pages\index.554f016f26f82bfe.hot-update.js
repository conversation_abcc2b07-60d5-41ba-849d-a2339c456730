"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 0,\n            y: 0\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot\n            drawRobot(ctx, robotState, canvas.width / 2, canvas.height / 2);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation = Math.sin(robotState.dancePhase * 1.5) * 0.4;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors\n        const headColor = \"#4A90E2\";\n        const bodyColor = \"#F5F5F5\";\n        const eyeColor = \"#2C3E50\";\n        const mouthColor = \"#E74C3C\";\n        const armColor = \"#34495E\";\n        // Draw robot body (main torso)\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, 20, 60, 80, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#2980B9\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.ellipse(0, -60, 50, 45, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes\n        ctx.fillStyle = eyeColor;\n        const eyeY = -70;\n        const eyeSize = 8 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(18, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 4;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -45, 12, 6 + robotState.mouthOpen * 10, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (line)\n            ctx.moveTo(-12, -45);\n            ctx.lineTo(12, -45);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                width: 400,\n                height: 400,\n                onClick: handleCanvasClick,\n                className: \"cursor-pointer\",\n                style: {\n                    maxWidth: \"100%\",\n                    height: \"auto\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            !imagesLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"Loading Daswos...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});