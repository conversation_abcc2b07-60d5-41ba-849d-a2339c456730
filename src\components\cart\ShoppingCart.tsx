'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Minus, Plus, Trash2, ShoppingBag } from 'lucide-react'
import { CartItem, Product } from '@/types'
import { formatPrice, calculateTrustLevel, getSphereLabel, getSphereColor } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

interface ShoppingCartProps {
  items: CartItem[]
  onUpdateQuantity: (itemId: string, quantity: number) => void
  onRemoveItem: (itemId: string) => void
  onCheckout?: () => void
  isLoading?: boolean
  className?: string
}

export function ShoppingCart({
  items,
  onUpdateQuantity,
  onRemoveItem,
  onCheckout,
  isLoading = false,
  className = ''
}: ShoppingCartProps) {
  const { toast } = useToast()

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
  const totalPrice = items.reduce((sum, item) => {
    const price = item.product?.price || 0
    return sum + (price * item.quantity)
  }, 0)

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 0) return
    
    if (newQuantity === 0) {
      onRemoveItem(itemId)
      toast({
        title: 'Item removed',
        description: 'Item has been removed from your cart.',
      })
    } else {
      onUpdateQuantity(itemId, newQuantity)
    }
  }

  const handleRemoveItem = (itemId: string, productName?: string) => {
    onRemoveItem(itemId)
    toast({
      title: 'Item removed',
      description: `${productName || 'Item'} has been removed from your cart.`,
    })
  }

  const handleCheckout = () => {
    if (items.length === 0) {
      toast({
        title: 'Cart is empty',
        description: 'Add some items to your cart before checking out.',
        variant: 'destructive',
      })
      return
    }
    onCheckout?.()
  }

  if (items.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <ShoppingBag className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Your cart is empty</h3>
          <p className="text-muted-foreground text-center">
            Start chatting with Daswos to discover and add products to your cart!
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Shopping Cart</span>
          <Badge variant="secondary">
            {totalItems} {totalItems === 1 ? 'item' : 'items'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {items.map((item) => {
            const product = item.product
            if (!product) return null

            const trustLevel = calculateTrustLevel(product.trustScore)
            const sphereColor = getSphereColor(product.sphere)

            return (
              <div
                key={item.id}
                className="flex items-center space-x-3 p-3 border rounded-lg"
              >
                {/* Product Image */}
                <div className="w-16 h-16 bg-muted rounded-md flex items-center justify-center overflow-hidden">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                  )}
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium truncate">{product.name}</h4>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="outline" className={sphereColor}>
                      {getSphereLabel(product.sphere)}
                    </Badge>
                    <Badge variant="outline" className={trustLevel.color}>
                      Trust: {product.trustScore}%
                    </Badge>
                  </div>
                  <p className="text-sm font-semibold mt-1">
                    {formatPrice(product.price)}
                  </p>
                </div>

                {/* Quantity Controls */}
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                    disabled={isLoading}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  
                  <span className="w-8 text-center text-sm font-medium">
                    {item.quantity}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                    disabled={isLoading}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>

                {/* Remove Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-destructive hover:text-destructive"
                  onClick={() => handleRemoveItem(item.id, product.name)}
                  disabled={isLoading}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            )
          })}
        </div>

        {/* Cart Summary */}
        <div className="border-t pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal ({totalItems} items):</span>
            <span className="font-medium">{formatPrice(totalPrice)}</span>
          </div>
          
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Shipping:</span>
            <span>Calculated at checkout</span>
          </div>
          
          <div className="flex justify-between text-lg font-semibold border-t pt-2">
            <span>Total:</span>
            <span>{formatPrice(totalPrice)}</span>
          </div>
        </div>

        {/* Checkout Button */}
        <Button
          onClick={handleCheckout}
          className="w-full"
          size="lg"
          disabled={isLoading || items.length === 0}
          loading={isLoading}
        >
          Proceed to Checkout
        </Button>

        {/* Trust Information */}
        <div className="text-xs text-muted-foreground text-center">
          <p>🛡️ All SafeSphere items are verified and protected</p>
          <p>⭐ Trust scores help you make informed decisions</p>
        </div>
      </CardContent>
    </Card>
  )
}
