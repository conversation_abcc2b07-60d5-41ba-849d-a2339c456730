'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { signUp } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'

const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  email: z.string().email('Please enter a valid email address'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>

interface RegisterFormProps {
  onSuccess?: (user: any) => void
  onSwitchToLogin?: () => void
}

export function RegisterForm({ onSuccess, onSwitchToLogin }: RegisterFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  })

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true)
    try {
      // TEMPORARY: For testing, just create a test user
      if (data.username === 'test' && data.password === 'test') {
        const testUser = {
          id: 'test-user-id',
          username: 'test',
          email: '<EMAIL>',
          fullName: data.fullName || 'Test User',
          isSeller: false,
          isAdmin: false,
          hasSubscription: true,
          trustScore: 85,
          verificationStatus: 'verified',
          tier: 'safesphere'
        }

        // Store in localStorage for session
        localStorage.setItem('daswos_user_id', testUser.id)
        localStorage.setItem('daswos_user_data', JSON.stringify(testUser))

        toast({
          title: 'Welcome to DasWos!',
          description: 'Test account created successfully.',
        })
        onSuccess?.(testUser)
        return
      }

      const { data: authData, error } = await signUp(data.email, data.password, {
        username: data.username,
        full_name: data.fullName,
      })

      if (error) {
        toast({
          title: 'Registration failed',
          description: error.message,
          variant: 'destructive',
        })
        return
      }

      if (authData.user) {
        toast({
          title: 'Welcome to DasWos!',
          description: 'Your account has been created successfully.',
        })
        onSuccess?.(authData.user)
      }
    } catch (error) {
      toast({
        title: 'Registration failed',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">Join DasWos AI</CardTitle>
        <CardDescription>
          Create your account to access AI-powered shopping
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              {...register('username')}
              type="text"
              placeholder="Choose a username"
              error={errors.username?.message}
              disabled={isLoading}
            />
          </div>

          <div>
            <Input
              {...register('fullName')}
              type="text"
              placeholder="Enter your full name"
              error={errors.fullName?.message}
              disabled={isLoading}
            />
          </div>

          <div>
            <Input
              {...register('email')}
              type="email"
              placeholder="Enter your email"
              error={errors.email?.message}
              disabled={isLoading}
            />
          </div>

          <div>
            <Input
              {...register('password')}
              type="password"
              placeholder="Create a password"
              error={errors.password?.message}
              disabled={isLoading}
            />
          </div>

          <div>
            <Input
              {...register('confirmPassword')}
              type="password"
              placeholder="Confirm your password"
              error={errors.confirmPassword?.message}
              disabled={isLoading}
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            loading={isLoading}
            disabled={isLoading}
          >
            Create Account
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="text-primary hover:underline font-medium"
            >
              Sign in
            </button>
          </p>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-muted-foreground">
            By creating an account, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
