import { NextApiRequest, NextApiResponse } from 'next'
import { transcribeAudio } from '@/lib/openai'
import formidable from 'formidable'
import fs from 'fs'

export const config = {
  api: {
    bodyParser: false,
  },
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
      keepExtensions: true,
    })

    const [fields, files] = await form.parse(req)
    const audioFile = Array.isArray(files.audio) ? files.audio[0] : files.audio

    if (!audioFile) {
      return res.status(400).json({ error: 'No audio file provided' })
    }

    // Read the audio file
    const audioBuffer = fs.readFileSync(audioFile.filepath)
    const audioBlob = new Blob([audioBuffer], { type: audioFile.mimetype || 'audio/webm' })

    // Transcribe using OpenAI Whisper
    const transcription = await transcribeAudio(audioBlob)

    // Clean up temporary file
    fs.unlinkSync(audioFile.filepath)

    res.status(200).json({ text: transcription })
  } catch (error) {
    console.error('Transcription error:', error)
    res.status(500).json({ 
      error: 'Failed to transcribe audio',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
