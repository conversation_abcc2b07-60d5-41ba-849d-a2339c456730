// User types
export interface User {
  id: string
  username: string
  email: string
  fullName: string
  businessName?: string
  contactInfo?: any
  address?: any
  trustScore: number
  verificationStatus: 'unverified' | 'pending' | 'verified'
  tier: 'guest' | 'opensphere' | 'safesphere'
  isSeller: boolean
  isAdmin: boolean
  createdAt: string
  updatedAt: string
}

// Product types
export interface Product {
  id: string
  name: string
  description?: string
  price: number
  image?: string
  trustScore: number
  sphere: 'opensphere' | 'safesphere'
  category?: string
  tags?: string[]
  sellerId: string
  seller?: User
  createdAt: string
  updatedAt: string
}

// Cart types
export interface CartItem {
  id: string
  userId: string
  productId: string
  quantity: number
  addedAt: string
  product?: Product
}

export interface Cart {
  items: CartItem[]
  totalItems: number
  totalPrice: number
}

// Chat types
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  metadata?: {
    audioUrl?: string
    recommendations?: ProductRecommendation[]
    actions?: ChatAction[]
  }
}

export interface ProductRecommendation {
  productId: string
  product?: Product
  reason: string
  confidence: number
}

export interface ChatAction {
  type: 'add_to_cart' | 'search_products' | 'show_product_details' | 'navigate'
  data: any
  label: string
}

// Voice types
export interface VoiceSettings {
  enabled: boolean
  autoSpeak: boolean
  voice: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer'
  speed: number
  volume: number
}

// Robot animation types
export interface RobotState {
  animation: 'idle' | 'talking' | 'listening' | 'thinking' | 'dancing' | 'searching'
  position: { x: number; y: number }
  scale: number
  rotation: number
}

// App state types
export interface AppState {
  user: User | null
  cart: Cart
  currentChat: ChatMessage[]
  robotState: RobotState
  voiceSettings: VoiceSettings
  isLoading: boolean
  error: string | null
}

// API response types
export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export interface SearchFilters {
  query?: string
  category?: string
  sphere?: 'safesphere' | 'opensphere'
  minPrice?: number
  maxPrice?: number
  minTrustScore?: number
  tags?: string[]
}

export interface SearchResults {
  products: Product[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  fullName: string
  password: string
  confirmPassword: string
}

export interface ChatForm {
  message: string
}

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
}

export interface InputProps extends BaseComponentProps {
  type?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  error?: string
}

// Event types
export interface VoiceEvent {
  type: 'start' | 'stop' | 'result' | 'error'
  data?: any
}

export interface RobotEvent {
  type: 'animation_change' | 'position_change' | 'interaction'
  data?: any
}

// Configuration types
export interface AppConfig {
  supabase: {
    url: string
    anonKey: string
  }
  openai: {
    apiKey: string
  }
  features: {
    voiceEnabled: boolean
    robotAnimations: boolean
    autoRecommendations: boolean
  }
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
}

// Utility types
export type Sphere = 'safesphere' | 'opensphere'
export type TrustLevel = 'low' | 'medium' | 'high' | 'excellent'
export type AnimationState = 'idle' | 'talking' | 'listening' | 'thinking' | 'dancing' | 'searching'
export type VoiceType = 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer'
