import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import DasWosCoinIcon from '@/components/shared/daswos-coin-icon';
import { formatDasWosCoins } from '@/lib/utils';
import { Package, Calendar, CreditCard, RefreshCcw, ShoppingBag, Coins, CheckCircle, Star } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { RatingPopup } from '@/components/rating-popup';

interface DasWosCoinTransaction {
  id: number;
  userId: number;
  type: 'spend';
  amount: number;
  description: string;
  metadata: {
    productId: number;
    productTitle: string;
    quantity: number;
    source: string;
  };
  timestamp: string;
  createdAt: string;
}

interface Order {
  id: number;
  userId: number;
  orderDate: string;
  totalAmount: number;
  status: string;
  paymentMethod: string;
  shippingAddress: string;
  billingAddress: string;
  paymentReference?: string;
  notes?: string;
  updatedAt?: string;
}

interface Purchase {
  id: string;
  type: 'order' | 'daswos_coin' | 'product_purchase';
  date: string;
  totalAmount: number;
  paymentMethod: string;
  status: string;
  items: Array<{
    productId: number;
    productTitle: string;
    quantity: number;
    price: number;
  }>;
  source?: string;
  purchaseId?: number; // For actual purchase records
}

interface ProductPurchase {
  id: number;
  buyerId: number;
  sellerId: number;
  productId: number;
  quantity: number;
  totalPrice: number;
  status: 'pending' | 'received' | 'disputed';
  purchasedAt: string;
  receivedAt?: string;
  rating?: number;
  reviewComment?: string;
  ratedAt?: string;
  product: {
    id: number;
    title: string;
    price: number;
    imageUrl: string;
    sellerName: string;
  };
}

const PurchasesPage: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Rating popup state
  const [ratingPopup, setRatingPopup] = useState<{
    isOpen: boolean;
    purchaseId?: number;
    productTitle?: string;
    sellerName?: string;
  }>({
    isOpen: false
  });

  // Fetch actual product purchases
  const { data: productPurchases, isLoading: loadingProductPurchases } = useQuery<ProductPurchase[]>({
    queryKey: ['/api/user/purchases'],
    queryFn: async () => {
      const response = await fetch('/api/user/purchases', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch purchases');
      }

      return response.json();
    },
    enabled: !!user,
  });

  // Fetch DasWos coin transactions
  const { data: coinTransactions, isLoading: loadingCoinTransactions } = useQuery<DasWosCoinTransaction[]>({
    queryKey: ['/api/user/daswos-coins/transactions'],
    queryFn: async () => {
      const response = await fetch('/api/user/daswos-coins/transactions', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch coin transactions');
      }

      const data = await response.json();
      // Filter only purchase transactions (type: 'spend')
      return data.filter((transaction: DasWosCoinTransaction) => transaction.type === 'spend');
    },
    enabled: !!user,
  });

  // Fetch regular orders
  const { data: orders, isLoading: loadingOrders } = useQuery<Order[]>({
    queryKey: ['/api/orders/user'],
    queryFn: async () => {
      const response = await fetch('/api/orders/user', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }

      return response.json();
    },
    enabled: !!user,
  });

  // Function to mark purchase as received
  const markAsReceived = async (purchaseId: number, productTitle: string, sellerName: string) => {
    try {
      const response = await fetch(`/api/user/purchases/${purchaseId}/received`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to mark purchase as received');
      }

      // Refresh the purchases data
      queryClient.invalidateQueries({ queryKey: ['/api/user/purchases'] });

      toast({
        title: "Item Received",
        description: "Thank you for confirming receipt! Please rate your experience.",
        variant: "default",
      });

      // Show rating popup
      setRatingPopup({
        isOpen: true,
        purchaseId,
        productTitle,
        sellerName
      });
    } catch (error) {
      console.error('Error marking purchase as received:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to mark purchase as received",
        variant: "destructive",
      });
    }
  };

  // Function to submit rating
  const submitRating = async (rating: number, comment?: string) => {
    if (!ratingPopup.purchaseId) return;

    try {
      const response = await fetch(`/api/user/purchases/${ratingPopup.purchaseId}/rating`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ rating, comment }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to submit rating');
      }

      // Refresh the purchases data
      queryClient.invalidateQueries({ queryKey: ['/api/user/purchases'] });

      toast({
        title: "Rating Submitted",
        description: "Thank you for your feedback! The seller's trust score has been updated.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error submitting rating:', error);
      throw error; // Let the popup handle the error
    }
  };

  // Combine and transform all purchases
  const purchases: Purchase[] = React.useMemo(() => {
    const allPurchases: Purchase[] = [];

    // Add actual product purchases
    if (productPurchases) {
      productPurchases.forEach(purchase => {
        allPurchases.push({
          id: `purchase-${purchase.id}`,
          type: 'product_purchase',
          date: purchase.purchasedAt,
          totalAmount: purchase.totalPrice,
          paymentMethod: 'DasWos Coins',
          status: purchase.status,
          items: [{
            productId: purchase.productId,
            productTitle: purchase.product.title,
            quantity: purchase.quantity,
            price: purchase.totalPrice
          }],
          purchaseId: purchase.id
        });
      });
    }

    // Add DasWos coin purchases (legacy) - only if no corresponding purchase record exists
    if (coinTransactions) {
      coinTransactions.forEach(transaction => {
        // Check if this transaction already has a corresponding purchase record
        const hasCorrespondingPurchase = productPurchases?.some(purchase =>
          purchase.product.title === transaction.metadata.productTitle &&
          purchase.totalPrice === transaction.amount
        );

        // Only add legacy transaction if no corresponding purchase record exists
        if (!hasCorrespondingPurchase) {
          allPurchases.push({
            id: `coin-${transaction.id}`,
            type: 'daswos_coin',
            date: transaction.createdAt || transaction.timestamp,
            totalAmount: transaction.amount,
            paymentMethod: 'DasWos Coins',
            status: 'completed',
            items: [{
              productId: transaction.metadata.productId,
              productTitle: transaction.metadata.productTitle,
              quantity: transaction.metadata.quantity,
              price: transaction.amount
            }],
            source: transaction.metadata.source
          });
        }
      });
    }

    // Add regular orders
    if (orders) {
      orders.forEach(order => {
        allPurchases.push({
          id: `order-${order.id}`,
          type: 'order',
          date: order.orderDate,
          totalAmount: order.totalAmount,
          paymentMethod: order.paymentMethod,
          status: order.status,
          items: [{
            productId: 0,
            productTitle: `Order #${order.id}`,
            quantity: 1,
            price: order.totalAmount
          }]
        });
      });
    }

    // Sort by date (newest first)
    return allPurchases.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [productPurchases, coinTransactions, orders]);

  const isLoading = loadingProductPurchases || loadingCoinTransactions || loadingOrders;
  const error = null; // We'll handle errors individually

  const refetch = () => {
    // Refetch both data sources
    window.location.reload();
  };

  // Format transaction date
  const formatTransactionDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-600 dark:text-gray-400">
              Please log in to view your purchase history.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            My Purchases
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            View your purchase history and transaction details
          </p>
        </div>

        {/* Purchase History Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Purchase History
                </CardTitle>
                <CardDescription>
                  All your purchases and order history
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isLoading}
              >
                <RefreshCcw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-600 dark:text-gray-400 mt-2">Loading purchases...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600 dark:text-red-400">
                  Failed to load purchase history. Please try again.
                </p>
              </div>
            ) : !purchases || purchases.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400 mb-2">No purchases yet</p>
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  Your purchases will appear here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {purchases.map((purchase) => (
                  <div
                    key={purchase.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {purchase.type === 'daswos_coin' ? (
                            <Coins className="h-4 w-4 text-yellow-500" />
                          ) : (
                            <ShoppingBag className="h-4 w-4 text-blue-500" />
                          )}
                          <div className="flex flex-col">
                            {purchase.items.map((item, index) => (
                              <h3 key={index} className="font-medium text-gray-900 dark:text-gray-100">
                                {item.productTitle}
                              </h3>
                            ))}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {purchase.type === 'daswos_coin' ? (
                              purchase.source === 'daswos_ai_buy' ? 'AI Purchase' : 'DasWos Coins'
                            ) : purchase.type === 'product_purchase' ? (
                              'Product Purchase'
                            ) : (
                              'Order'
                            )}
                          </Badge>
                          <Badge
                            variant={
                              purchase.status === 'completed' || purchase.status === 'received'
                                ? 'default'
                                : purchase.status === 'pending'
                                  ? 'secondary'
                                  : 'destructive'
                            }
                            className="text-xs"
                          >
                            {purchase.status === 'pending' ? 'Awaiting Delivery' : purchase.status}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatTransactionDate(purchase.date)}
                          </div>
                          <div className="flex items-center gap-1">
                            <CreditCard className="h-3 w-3" />
                            {purchase.paymentMethod}
                          </div>
                          <div>
                            Items: {purchase.items.reduce((sum, item) => sum + item.quantity, 0)}
                          </div>
                        </div>

                        {purchase.items.length > 1 && (
                          <div className="mt-2">
                            <p className="text-xs text-gray-500 dark:text-gray-500">
                              {purchase.items.length} different products
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="text-right flex flex-col items-end gap-2">
                        <div className="flex items-center font-semibold">
                          {purchase.type === 'daswos_coin' || purchase.type === 'product_purchase' ? (
                            <div className="flex items-center text-red-600 dark:text-red-400">
                              <DasWosCoinIcon size={16} className="mr-1" />
                              -{formatDasWosCoins(purchase.totalAmount)}
                            </div>
                          ) : (
                            <div className="text-gray-900 dark:text-gray-100">
                              ${(purchase.totalAmount / 100).toFixed(2)}
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-500">
                          Order #{purchase.id.split('-')[1]}
                        </div>

                        {/* Item Received Button for pending product purchases */}
                        {purchase.type === 'product_purchase' && purchase.status === 'pending' && purchase.purchaseId && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const productPurchase = productPurchases?.find(p => p.id === purchase.purchaseId);
                              markAsReceived(
                                purchase.purchaseId!,
                                purchase.items[0].productTitle,
                                productPurchase?.product.sellerName || 'Unknown Seller'
                              );
                            }}
                            className="text-xs bg-green-50 hover:bg-green-100 border-green-200 text-green-700 dark:bg-green-900/20 dark:hover:bg-green-900/30 dark:border-green-800 dark:text-green-400"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Item Received
                          </Button>
                        )}

                        {/* Show received status and rating */}
                        {purchase.type === 'product_purchase' && purchase.status === 'received' && (
                          <div className="flex flex-col items-end gap-1">
                            <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Received
                            </div>
                            {(() => {
                              const productPurchase = productPurchases?.find(p => p.id === purchase.purchaseId);
                              if (productPurchase?.rating) {
                                return (
                                  <div className="flex items-center text-xs text-yellow-600 dark:text-yellow-400">
                                    <Star className="h-3 w-3 mr-1 fill-current" />
                                    {productPurchase.rating}/5 stars
                                  </div>
                                );
                              }
                              return null;
                            })()}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Summary Card */}
        {purchases && purchases.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Purchase Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {purchases.length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Total Purchases
                  </div>
                </div>

                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center justify-center">
                    <DasWosCoinIcon size={20} className="mr-1" />
                    {formatDasWosCoins(
                      purchases
                        .filter(p => p.type === 'daswos_coin')
                        .reduce((sum, p) => sum + p.totalAmount, 0)
                    )}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    DasWos Coins Spent
                  </div>
                </div>

                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    ${(
                      purchases
                        .filter(p => p.type === 'order')
                        .reduce((sum, p) => sum + p.totalAmount, 0) / 100
                    ).toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Money Spent
                  </div>
                </div>

                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {purchases.reduce((sum, p) => sum + p.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Items Purchased
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Rating Popup */}
      <RatingPopup
        isOpen={ratingPopup.isOpen}
        onClose={() => setRatingPopup({ isOpen: false })}
        onSubmit={submitRating}
        productTitle={ratingPopup.productTitle || ''}
        sellerName={ratingPopup.sellerName || ''}
      />
    </div>
  );
};

export default PurchasesPage;
