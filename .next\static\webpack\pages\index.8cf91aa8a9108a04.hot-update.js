"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/robot/RobotInterface.tsx":
/*!*************************************************!*\
  !*** ./src/components/robot/RobotInterface.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RobotInterface: function() { return /* binding */ RobotInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ RobotInterface auto */ \nvar _s = $RefreshSig$();\n\nfunction RobotInterface(param) {\n    let { state = \"idle\", onInteraction, scale = 0.8, className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Robot animation state\n    const robotStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        currentState: state,\n        position: {\n            x: 250,\n            y: 250\n        },\n        velocity: {\n            x: 1,\n            y: 0.5\n        },\n        targetPosition: {\n            x: 250,\n            y: 250\n        },\n        scale: scale,\n        rotation: 0,\n        headBobAmount: 0,\n        bodyRotation: 0,\n        armRotations: {\n            left: 0,\n            right: 0\n        },\n        animationTime: 0,\n        isBlinking: false,\n        blinkTimer: 0,\n        talkPulse: 0,\n        dancePhase: 0,\n        searchAngle: 0,\n        eyeScale: 1,\n        mouthScale: 1,\n        mouthOpen: 0,\n        moveTimer: 0,\n        isMoving: true\n    });\n    // No need to load images - we'll draw the robot with canvas primitives\n    // Update robot state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        robotStateRef.current.currentState = state;\n        robotStateRef.current.scale = scale;\n    }, [\n        state,\n        scale\n    ]);\n    // Animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const animate = (timestamp)=>{\n            const robotState = robotStateRef.current;\n            robotState.animationTime = timestamp;\n            // Clear canvas\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // Update animation based on current state\n            updateRobotAnimation(robotState, timestamp);\n            // Draw robot at its current position\n            drawRobot(ctx, robotState, robotState.position.x, robotState.position.y);\n            animationFrameRef.current = requestAnimationFrame(animate);\n        };\n        animationFrameRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n            }\n        };\n    }, []);\n    const updateRobotAnimation = (robotState, timestamp)=>{\n        const timeInSeconds = timestamp * 0.001;\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        // Handle free movement around the screen\n        robotState.moveTimer += 0.016;\n        if (robotState.isMoving) {\n            // Update position\n            robotState.position.x += robotState.velocity.x;\n            robotState.position.y += robotState.velocity.y;\n            // Bounce off walls\n            const margin = 80 // Robot size margin\n            ;\n            if (robotState.position.x <= margin || robotState.position.x >= canvas.width - margin) {\n                robotState.velocity.x *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            if (robotState.position.y <= margin || robotState.position.y >= canvas.height - margin) {\n                robotState.velocity.y *= -1;\n                robotState.rotation += 0.1 // Spin when bouncing\n                ;\n            }\n            // Keep within bounds\n            robotState.position.x = Math.max(margin, Math.min(canvas.width - margin, robotState.position.x));\n            robotState.position.y = Math.max(margin, Math.min(canvas.height - margin, robotState.position.y));\n            // Randomly change direction occasionally\n            if (Math.random() < 0.005) {\n                robotState.velocity.x = (Math.random() - 0.5) * 3;\n                robotState.velocity.y = (Math.random() - 0.5) * 3;\n                robotState.rotation += (Math.random() - 0.5) * 0.5;\n            }\n        }\n        // Handle blinking\n        robotState.blinkTimer += 0.016 // ~60fps\n        ;\n        if (robotState.blinkTimer > 3 + Math.random() * 2) {\n            robotState.isBlinking = true;\n            robotState.blinkTimer = 0;\n        }\n        if (robotState.isBlinking) {\n            robotState.eyeScale = Math.max(0.1, 1 - robotState.blinkTimer * 10);\n            if (robotState.blinkTimer > 0.2) {\n                robotState.isBlinking = false;\n                robotState.eyeScale = 1;\n            }\n        }\n        switch(robotState.currentState){\n            case \"idle\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 2) * 8;\n                robotState.armRotations.left = Math.sin(timeInSeconds) * 0.1;\n                robotState.armRotations.right = Math.sin(timeInSeconds + Math.PI) * 0.1;\n                robotState.mouthOpen = 0;\n                robotState.isMoving = true // Allow movement when idle\n                ;\n                break;\n            case \"talking\":\n                robotState.talkPulse = Math.sin(timeInSeconds * 15) * 0.1;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 8) * 12;\n                robotState.armRotations.left = Math.sin(timeInSeconds * 6) * 0.3;\n                robotState.armRotations.right = Math.sin(timeInSeconds * 6 + Math.PI) * 0.3;\n                robotState.mouthOpen = Math.abs(Math.sin(timeInSeconds * 12)) * 0.8;\n                robotState.eyeScale = 1 + Math.sin(timeInSeconds * 8) * 0.1;\n                robotState.isMoving = false // Stop moving when talking\n                ;\n                break;\n            case \"listening\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 3) * 5;\n                robotState.rotation = Math.sin(timeInSeconds * 2) * 0.15;\n                robotState.mouthOpen = 0.2;\n                robotState.eyeScale = 1.2 // Wider eyes when listening\n                ;\n                robotState.isMoving = false // Stop moving when listening\n                ;\n                break;\n            case \"thinking\":\n                robotState.headBobAmount = Math.sin(timeInSeconds * 1.5) * 6;\n                robotState.rotation = Math.sin(timeInSeconds * 0.8) * 0.25;\n                robotState.mouthOpen = 0.1;\n                robotState.eyeScale = 0.8 // Squinted eyes when thinking\n                ;\n                robotState.isMoving = false // Stop moving when thinking\n                ;\n                break;\n            case \"dancing\":\n                robotState.dancePhase += 0.08;\n                robotState.headBobAmount = Math.sin(robotState.dancePhase * 3) * 15;\n                robotState.rotation += 0.05 // Continuous spinning while dancing\n                ;\n                robotState.armRotations.left = Math.sin(robotState.dancePhase * 2) * 0.6;\n                robotState.armRotations.right = Math.sin(robotState.dancePhase * 2 + Math.PI) * 0.6;\n                robotState.mouthOpen = 0.6 // Happy expression\n                ;\n                robotState.eyeScale = 1.3 // Excited eyes\n                ;\n                // Move faster while dancing\n                robotState.velocity.x *= 1.1;\n                robotState.velocity.y *= 1.1;\n                break;\n            case \"searching\":\n                robotState.searchAngle += 0.04;\n                robotState.rotation = Math.sin(robotState.searchAngle) * 0.4;\n                robotState.headBobAmount = Math.sin(timeInSeconds * 4) * 8;\n                robotState.armRotations.left = Math.sin(robotState.searchAngle * 0.7) * 0.25;\n                robotState.armRotations.right = Math.sin(robotState.searchAngle * 0.7 + Math.PI) * 0.25;\n                robotState.mouthOpen = 0.3;\n                robotState.eyeScale = 1.1;\n                break;\n        }\n    };\n    const drawRobot = (ctx, robotState, centerX, centerY)=>{\n        ctx.save();\n        // Move to center and apply transformations\n        ctx.translate(centerX, centerY + robotState.headBobAmount);\n        ctx.scale(robotState.scale, robotState.scale);\n        ctx.rotate(robotState.rotation);\n        // Apply talk pulse if talking\n        if (robotState.currentState === \"talking\") {\n            ctx.scale(1 + robotState.talkPulse, 1 + robotState.talkPulse);\n        }\n        // Robot colors - matching your robot images\n        const headColor = \"#5B9BD5\" // Blue head like in your images\n        ;\n        const bodyColor = \"#FFFFFF\" // White body\n        ;\n        const eyeColor = \"#2C3E50\" // Dark eyes\n        ;\n        const mouthColor = \"#FF6B6B\" // Red mouth\n        ;\n        const armColor = \"#4A4A4A\" // Dark gray arms\n        ;\n        // Draw robot body (main torso) - more rounded like your images\n        ctx.fillStyle = bodyColor;\n        ctx.strokeStyle = \"#E0E0E0\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, 10, 55, 70, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw robot head - more rounded and friendly\n        ctx.fillStyle = headColor;\n        ctx.strokeStyle = \"#4A90E2\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.ellipse(0, -50, 45, 40, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        // Draw eyes - larger and more expressive like your robot\n        ctx.fillStyle = eyeColor;\n        const eyeY = -55;\n        const eyeSize = 6 * robotState.eyeScale;\n        // Left eye\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Right eye\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY, eyeSize, eyeSize, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Add eye highlights for more life\n        ctx.fillStyle = \"#FFFFFF\";\n        ctx.beginPath();\n        ctx.ellipse(-15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.beginPath();\n        ctx.ellipse(15, eyeY - 1, 2, 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw mouth - more curved and friendly\n        ctx.strokeStyle = mouthColor;\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        if (robotState.mouthOpen > 0) {\n            // Open mouth (oval)\n            ctx.ellipse(0, -35, 10, 4 + robotState.mouthOpen * 8, 0, 0, Math.PI * 2);\n            ctx.stroke();\n        } else {\n            // Closed mouth (curved line)\n            ctx.arc(0, -30, 8, 0.2, Math.PI - 0.2);\n            ctx.stroke();\n        }\n        // Draw left arm\n        ctx.save();\n        ctx.translate(-70, 0);\n        ctx.rotate(robotState.armRotations.left);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw right arm\n        ctx.save();\n        ctx.translate(70, 0);\n        ctx.rotate(robotState.armRotations.right);\n        ctx.fillStyle = armColor;\n        ctx.strokeStyle = \"#2C3E50\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-8, -25, 16, 50, 8);\n        ctx.fill();\n        ctx.stroke();\n        ctx.restore();\n        // Draw antenna\n        ctx.strokeStyle = \"#7F8C8D\";\n        ctx.lineWidth = 3;\n        ctx.beginPath();\n        ctx.moveTo(0, -105);\n        ctx.lineTo(0, -120);\n        ctx.stroke();\n        // Antenna tip\n        ctx.fillStyle = \"#E74C3C\";\n        ctx.beginPath();\n        ctx.ellipse(0, -125, 4, 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        // Draw chest panel\n        ctx.fillStyle = \"#ECF0F1\";\n        ctx.strokeStyle = \"#BDC3C7\";\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.roundRect(-25, 0, 50, 30, 5);\n        ctx.fill();\n        ctx.stroke();\n        // Draw chest buttons\n        ctx.fillStyle = \"#3498DB\";\n        for(let i = 0; i < 3; i++){\n            ctx.beginPath();\n            ctx.ellipse(-15 + i * 15, 15, 3, 3, 0, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        ctx.restore();\n    };\n    const handleCanvasClick = (event)=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const y = event.clientY - rect.top;\n        // Check if click is on robot (simple distance check)\n        const centerX = canvas.width / 2;\n        const centerY = canvas.height / 2;\n        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);\n        if (distance < 100 * scale) {\n            onInteraction === null || onInteraction === void 0 ? void 0 : onInteraction(\"robot_click\", {\n                x,\n                y\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"robot-interface \".concat(className, \" relative\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            width: 600,\n            height: 600,\n            onClick: handleCanvasClick,\n            className: \"cursor-pointer w-full h-full\",\n            style: {\n                maxWidth: \"100%\",\n                maxHeight: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n            lineNumber: 353,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\tempfile\\\\daswos full ai app\\\\src\\\\components\\\\robot\\\\RobotInterface.tsx\",\n        lineNumber: 352,\n        columnNumber: 5\n    }, this);\n}\n_s(RobotInterface, \"14h1+qOErNn1xTmBd5zFwKXSVio=\");\n_c = RobotInterface;\nvar _c;\n$RefreshReg$(_c, \"RobotInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/robot/RobotInterface.tsx\n"));

/***/ })

});