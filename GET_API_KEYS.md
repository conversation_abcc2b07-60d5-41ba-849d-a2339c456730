# Get Your API Keys - Quick Guide

## 🔑 Supabase API Keys

Your Supabase project URL has been configured: `https://nldvdkdredobzgmcjajp.supabase.co`

### To get your Supabase API keys:

1. **Go to Supabase Dashboard**
   - Visit: https://supabase.com/dashboard
   - Sign in to your account

2. **Select Your Project**
   - Look for project: `nldvdkdredobzgmcjajp`
   - Click on it to open

3. **Get API Keys**
   - Go to **Settings** → **API**
   - Copy the following keys:

   **Anon/Public Key** (starts with `eyJ...`):
   ```
   Replace: NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
   With: NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...your_actual_anon_key
   ```

   **Service Role Key** (starts with `eyJ...`):
   ```
   Replace: SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
   With: SUPABASE_SERVICE_ROLE_KEY=eyJ...your_actual_service_role_key
   ```

## 🤖 OpenAI API Key

### To get your OpenAI API key:

1. **Go to OpenAI Platform**
   - Visit: https://platform.openai.com
   - Sign in or create an account

2. **Create API Key**
   - Go to **API Keys** section
   - Click **"Create new secret key"**
   - Give it a name like "DasWos AI App"
   - Copy the key (starts with `sk-...`)

3. **Update Environment**
   ```
   Replace: OPENAI_API_KEY=your_openai_api_key_here
   With: OPENAI_API_KEY=sk-...your_actual_openai_key
   ```

## 📝 Update Your .env.local File

After getting your keys, your `.env.local` should look like:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://nldvdkdredobzgmcjajp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...your_actual_anon_key
SUPABASE_SERVICE_ROLE_KEY=eyJ...your_actual_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=sk-...your_actual_openai_key

# Database Configuration
DATABASE_URL=********************************************************************************************/postgres

# App Configuration
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Development
NODE_ENV=development
```

## 🔄 Restart the Application

After updating your keys:

1. **Stop the current server** (Ctrl+C in terminal)
2. **Restart the development server**:
   ```bash
   npm run dev
   ```
3. **Refresh your browser** at http://localhost:3000

## ✅ Test Your Setup

Once you have the keys configured:

1. **Test Authentication**: Try to sign up/login
2. **Test Database**: Check if you can see existing products
3. **Test AI Chat**: Ask Daswos "Show me some products"
4. **Test Voice**: Click the microphone and speak

## 🚨 Important Security Notes

- **Never commit** your `.env.local` file to version control
- **Keep your service role key secret** - it has admin access
- **Monitor your OpenAI usage** - API calls cost money
- **Set up billing alerts** in OpenAI dashboard

## 🛠️ Troubleshooting

**If authentication fails:**
- Check your Supabase anon key
- Verify your project URL is correct

**If AI chat doesn't work:**
- Check your OpenAI API key
- Verify you have credits in your OpenAI account

**If database queries fail:**
- Check your service role key
- Verify your database tables exist

## 📞 Need Help?

If you encounter issues:
1. Check the browser console for errors
2. Look at the terminal output for error messages
3. Verify all environment variables are set correctly
4. Make sure your Supabase project is active

Once you have your API keys, the DasWos AI app will be fully functional! 🚀
