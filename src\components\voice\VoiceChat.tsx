'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Mic, MicOff, Send, Volume2, VolumeX } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { ChatMessage, VoiceSettings } from '@/types'

interface VoiceChatProps {
  messages: ChatMessage[]
  onSendMessage: (message: string, isVoice?: boolean) => void
  onVoiceSettingsChange?: (settings: VoiceSettings) => void
  isLoading?: boolean
  className?: string
}

export function VoiceChat({
  messages,
  onSendMessage,
  onVoiceSettingsChange,
  isLoading = false,
  className = ''
}: VoiceChatProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [textInput, setTextInput] = useState('')
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
    enabled: true,
    autoSpeak: true,
    voice: 'nova',
    speed: 1,
    volume: 0.8
  })

  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Initialize voice recognition
  useEffect(() => {
    if (!voiceSettings.enabled) return

    const checkMicrophonePermission = async () => {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true })
      } catch (error) {
        toast({
          title: 'Microphone access denied',
          description: 'Please allow microphone access to use voice features.',
          variant: 'destructive',
        })
        setVoiceSettings(prev => ({ ...prev, enabled: false }))
      }
    }

    checkMicrophonePermission()
  }, [voiceSettings.enabled, toast])

  const startRecording = async () => {
    if (!voiceSettings.enabled) {
      toast({
        title: 'Voice disabled',
        description: 'Please enable voice features first.',
        variant: 'destructive',
      })
      return
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' })
        await processVoiceInput(audioBlob)
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorder.start()
      setIsRecording(true)
    } catch (error) {
      toast({
        title: 'Recording failed',
        description: 'Could not start voice recording. Please check your microphone.',
        variant: 'destructive',
      })
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const processVoiceInput = async (audioBlob: Blob) => {
    try {
      // Send audio to transcription API
      const formData = new FormData()
      formData.append('audio', audioBlob)

      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Transcription failed')
      }

      const { text } = await response.json()
      
      if (text.trim()) {
        onSendMessage(text, true)
      } else {
        toast({
          title: 'No speech detected',
          description: 'Please try speaking more clearly.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Voice processing failed',
        description: 'Could not process your voice input. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleTextSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (textInput.trim() && !isLoading) {
      onSendMessage(textInput.trim())
      setTextInput('')
    }
  }

  const toggleVoiceSettings = (setting: keyof VoiceSettings, value?: any) => {
    const newSettings = {
      ...voiceSettings,
      [setting]: value !== undefined ? value : !voiceSettings[setting]
    }
    setVoiceSettings(newSettings)
    onVoiceSettingsChange?.(newSettings)
  }

  const speakMessage = async (text: string) => {
    if (!voiceSettings.enabled || !voiceSettings.autoSpeak) return

    try {
      const response = await fetch('/api/speak', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          voice: voiceSettings.voice,
          speed: voiceSettings.speed,
        }),
      })

      if (response.ok) {
        const audioBlob = await response.blob()
        const audioUrl = URL.createObjectURL(audioBlob)
        const audio = new Audio(audioUrl)
        audio.volume = voiceSettings.volume
        audio.play()
      }
    } catch (error) {
      console.error('Text-to-speech failed:', error)
    }
  }

  // Auto-speak assistant messages
  useEffect(() => {
    const lastMessage = messages[messages.length - 1]
    if (lastMessage && lastMessage.role === 'assistant' && voiceSettings.autoSpeak) {
      speakMessage(lastMessage.content)
    }
  }, [messages, voiceSettings.autoSpeak])

  return (
    <div className={`voice-chat flex flex-col h-full ${className}`}>
      {/* Voice Settings */}
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="font-semibold">Chat with Daswos</h3>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => toggleVoiceSettings('enabled')}
            title={voiceSettings.enabled ? 'Disable voice' : 'Enable voice'}
          >
            {voiceSettings.enabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => toggleVoiceSettings('autoSpeak')}
            title={voiceSettings.autoSpeak ? 'Disable auto-speak' : 'Enable auto-speak'}
          >
            {voiceSettings.autoSpeak ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <p>Start a conversation with Daswos!</p>
            <p className="text-sm mt-2">You can type or use voice commands.</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <Card className={`max-w-[80%] ${
                message.role === 'user' 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted'
              }`}>
                <CardContent className="p-3">
                  <p className="text-sm">{message.content}</p>
                  {message.metadata?.recommendations && (
                    <div className="mt-2 space-y-1">
                      <p className="text-xs opacity-75">Recommended products:</p>
                      {message.metadata.recommendations.map((rec: any, index: number) => (
                        <div key={index} className="text-xs bg-background/20 rounded p-1">
                          {rec.reason}
                        </div>
                      ))}
                    </div>
                  )}
                  <p className="text-xs opacity-50 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </CardContent>
              </Card>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t">
        <form onSubmit={handleTextSubmit} className="flex space-x-2">
          <Input
            value={textInput}
            onChange={(e) => setTextInput(e.target.value)}
            placeholder="Type your message or use voice..."
            disabled={isLoading}
            className="flex-1"
          />
          
          {voiceSettings.enabled && (
            <Button
              type="button"
              variant={isRecording ? "destructive" : "outline"}
              size="icon"
              onClick={isRecording ? stopRecording : startRecording}
              disabled={isLoading}
            >
              {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>
          )}
          
          <Button
            type="submit"
            disabled={!textInput.trim() || isLoading}
            loading={isLoading}
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
        
        {isRecording && (
          <div className="mt-2 text-center">
            <div className="inline-flex items-center space-x-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span>Recording... Click mic to stop</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
